from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import math
import numpy as np
from typing import Dict, List, Any, Optional
import cmath  # For complex number operations in AC analysis

app = Flask(__name__)
CORS(app)

class Component:
    def __init__(self, id: str, type: str, value: float, position: Dict[str, float]):
        self.id = id
        self.type = type
        self.value = value
        self.position = position
        self.connections = []

class Circuit:
    def __init__(self):
        self.components = {}
        self.wires = []
        self.nodes = {}
    
    def add_component(self, component_data):
        component = Component(
            component_data['id'],
            component_data['type'],
            component_data.get('value', 0),
            component_data['position']
        )
        self.components[component.id] = component
        return component
    
    def add_wire(self, wire_data):
        self.wires.append(wire_data)
    
    def simulate(self):
        """Enhanced circuit simulation using <PERSON><PERSON>'s law and <PERSON><PERSON><PERSON>'s laws"""
        results = {}

        # Find voltage sources
        voltage_sources = [c for c in self.components.values() if c.type == 'battery']
        resistors = [c for c in self.components.values() if c.type == 'resistor']
        leds = [c for c in self.components.values() if c.type == 'led']
        diodes = [c for c in self.components.values() if c.type == 'diode']

        if not voltage_sources:
            return {"error": "No voltage source found"}

        # Build circuit network
        circuit_network = self._build_network()

        if self._is_series_circuit():
            # Series circuit analysis
            total_voltage = sum(v.value for v in voltage_sources)
            total_resistance = self._calculate_series_resistance()

            if total_resistance > 0:
                current = total_voltage / total_resistance

                # Calculate voltage drops for each component
                for component in self.components.values():
                    if component.type == 'resistor':
                        voltage_drop = current * component.value
                        power = voltage_drop * current
                        results[component.id] = {
                            'voltage': voltage_drop,
                            'current': current,
                            'power': power,
                            'resistance': component.value
                        }
                    elif component.type == 'led':
                        # Use component-specific forward voltage
                        color = getattr(component, 'color', 'red')
                        forward_voltages = {'red': 1.8, 'green': 2.1, 'blue': 3.2, 'yellow': 2.0, 'white': 3.2}
                        forward_voltage = forward_voltages.get(color, 2.0)

                        led_current = min(current, 0.02)  # Max 20mA
                        brightness = min(led_current / 0.02, 1.0)
                        power = forward_voltage * led_current

                        results[component.id] = {
                            'voltage': forward_voltage,
                            'current': led_current,
                            'power': power,
                            'brightness': brightness,
                            'on': led_current > 0.001
                        }
                    elif component.type == 'diode':
                        diode_type = getattr(component, 'diode_type', 'silicon')
                        forward_voltages = {'silicon': 0.7, 'germanium': 0.3, 'schottky': 0.2}
                        forward_voltage = forward_voltages.get(diode_type, 0.7)

                        results[component.id] = {
                            'voltage': forward_voltage,
                            'current': current,
                            'power': forward_voltage * current,
                            'conducting': current > 0.001
                        }
                    elif component.type == 'tunnel_diode':
                        # Tunnel diode has negative resistance region
                        peak_current = getattr(component, 'peak_current', 0.01)  # 10mA default
                        peak_voltage = getattr(component, 'peak_voltage', 0.1)   # 0.1V default
                        valley_voltage = getattr(component, 'valley_voltage', 0.35)  # 0.35V default

                        # Simplified tunnel diode characteristic
                        if current < peak_current:
                            voltage = peak_voltage * (current / peak_current)
                        else:
                            voltage = valley_voltage

                        results[component.id] = {
                            'voltage': voltage,
                            'current': current,
                            'power': voltage * current,
                            'conducting': current > 0.001,
                            'negative_resistance': current > peak_current * 0.5 and current < peak_current * 1.5
                        }
                    elif component.type == 'zener_diode':
                        # Zener diode regulation
                        zener_voltage = getattr(component, 'zener_voltage', 5.1)
                        power_rating = getattr(component, 'power_rating', 0.5)

                        # Check if reverse biased and above breakdown
                        if current > 0.001:  # Forward biased
                            voltage = 0.7  # Standard forward voltage
                        else:  # Reverse biased - check for breakdown
                            voltage = min(zener_voltage, total_voltage)

                        max_current = power_rating / zener_voltage
                        actual_current = min(current, max_current)

                        results[component.id] = {
                            'voltage': voltage,
                            'current': actual_current,
                            'power': voltage * actual_current,
                            'regulating': voltage >= zener_voltage * 0.95,
                            'zener_voltage': zener_voltage
                        }
                    elif component.type == 'schottky_diode':
                        # Schottky diode - lower forward voltage, faster switching
                        forward_voltage = 0.2  # Lower than silicon
                        switching_speed = getattr(component, 'switching_speed', 1e-9)  # 1ns typical

                        results[component.id] = {
                            'voltage': forward_voltage,
                            'current': current,
                            'power': forward_voltage * current,
                            'conducting': current > 0.001,
                            'switching_speed': switching_speed
                        }
                    elif component.type == 'photodiode':
                        # Photodiode - light dependent current
                        dark_current = getattr(component, 'dark_current', 1e-9)
                        responsivity = getattr(component, 'responsivity', 0.5)
                        light_intensity = getattr(component, 'light_intensity', 0.001)  # W/m²

                        photo_current = responsivity * light_intensity + dark_current

                        results[component.id] = {
                            'voltage': 0.7 if current > 0.001 else 0,
                            'current': photo_current,
                            'power': 0.7 * photo_current if current > 0.001 else 0,
                            'photo_current': photo_current,
                            'dark_current': dark_current
                        }
                    elif component.type == 'capacitor':
                        # For DC analysis, capacitor acts as open circuit
                        results[component.id] = {
                            'voltage': 0,
                            'current': 0,
                            'power': 0,
                            'charged': False
                        }
                    elif component.type == 'function_generator':
                        # Function generator provides AC signal
                        frequency = getattr(component, 'frequency', 1000)
                        amplitude = getattr(component, 'amplitude', 5)
                        waveform = getattr(component, 'waveform', 'sine')

                        results[component.id] = {
                            'voltage': amplitude,
                            'frequency': frequency,
                            'waveform': waveform,
                            'current': 0.01,  # Small output current
                            'power': amplitude * 0.01,
                            'signal_type': 'ac'
                        }
                    elif component.type == 'signal_generator':
                        # Signal generator provides precise AC signals
                        frequency = getattr(component, 'frequency', 1000)
                        amplitude = getattr(component, 'amplitude', 3)

                        results[component.id] = {
                            'voltage': amplitude,
                            'frequency': frequency,
                            'current': 0.005,  # Very low output current
                            'power': amplitude * 0.005,
                            'signal_type': 'ac'
                        }
                    elif component.type == 'pulse_generator':
                        # Pulse generator provides digital signals
                        frequency = getattr(component, 'frequency', 1000)
                        amplitude = getattr(component, 'amplitude', 5)

                        results[component.id] = {
                            'voltage': amplitude,
                            'frequency': frequency,
                            'current': 0.01,
                            'power': amplitude * 0.01,
                            'signal_type': 'pulse'
                        }
                    elif component.type == 'oscilloscope_probe':
                        # Oscilloscope probe measures signals
                        results[component.id] = {
                            'voltage': 0,  # Will be updated based on connected signal
                            'current': 0,  # High impedance, minimal current
                            'impedance': 1e6,  # 1 MΩ input impedance
                            'attenuation': getattr(component, 'attenuation', 1),
                            'measurement_type': 'voltage'
                        }
                    elif component.type == 'current_probe':
                        # Current probe measures current flow
                        results[component.id] = {
                            'current_measured': current,  # Measured current
                            'voltage_output': current * 0.1,  # 100 mV/A sensitivity
                            'bandwidth': getattr(component, 'bandwidth', 1e6),
                            'measurement_type': 'current'
                        }
                    elif component.type == 'test_point':
                        # Test point provides easy access
                        results[component.id] = {
                            'voltage': 0,  # Will reflect connected circuit voltage
                            'current': current,
                            'resistance': 0.001,  # Very low contact resistance
                            'measurement_type': 'access_point'
                        }
                    elif component.type == 'igbt':
                        # IGBT - high power switching
                        gate_threshold = getattr(component, 'gate_threshold', 4.0)
                        on_resistance = getattr(component, 'on_resistance', 0.01)
                        max_current = getattr(component, 'max_current', 25)

                        # Simplified IGBT model
                        gate_voltage = getattr(component, 'gate_voltage', 0)
                        if gate_voltage > gate_threshold:
                            # IGBT is on
                            voltage_drop = current * on_resistance
                            actual_current = min(current, max_current)
                        else:
                            # IGBT is off
                            voltage_drop = total_voltage
                            actual_current = 0.001  # Leakage current

                        results[component.id] = {
                            'voltage': voltage_drop,
                            'current': actual_current,
                            'power': voltage_drop * actual_current,
                            'on_state': gate_voltage > gate_threshold,
                            'gate_threshold': gate_threshold
                        }
                    elif component.type == 'darlington_transistor':
                        # Darlington pair - very high gain
                        beta = getattr(component, 'beta', 1000)
                        base_current = getattr(component, 'base_current', 0.001)
                        saturation_voltage = getattr(component, 'saturation_voltage', 1.5)

                        # Calculate collector current
                        collector_current = beta * base_current

                        results[component.id] = {
                            'voltage': saturation_voltage if collector_current > 0.001 else total_voltage,
                            'current': collector_current,
                            'power': saturation_voltage * collector_current,
                            'beta': beta,
                            'base_current': base_current,
                            'saturated': collector_current > 0.001
                        }
        else:
            # More complex circuit analysis (parallel/mixed)
            results = self._analyze_complex_circuit()

        return results

    def _build_network(self):
        """Build a network representation of the circuit"""
        network = {'nodes': {}, 'branches': []}

        # Create nodes from wire connections
        node_id = 0
        for wire in self.wires:
            # This is a simplified network builder
            # In a full implementation, this would create a proper node-branch network
            pass

        return network

    def _is_series_circuit(self):
        """Check if the circuit is a simple series circuit"""
        # Simplified check - in reality, this would analyze the network topology
        return len(self.wires) <= len(self.components)

    def _calculate_series_resistance(self):
        """Calculate total resistance in series"""
        total_resistance = 0

        for component in self.components.values():
            if component.type == 'resistor':
                total_resistance += component.value
            elif component.type == 'led':
                # LED has small internal resistance
                total_resistance += 10  # Approximate internal resistance
            elif component.type == 'diode':
                # Forward-biased diode has small resistance
                total_resistance += 1

        return total_resistance

    def _analyze_complex_circuit(self):
        """Enhanced analysis for parallel and complex circuits using nodal analysis"""
        results = {}

        # Build circuit matrix for nodal analysis
        circuit_matrix = self._build_circuit_matrix()

        if circuit_matrix:
            # Solve the circuit using matrix methods
            node_voltages = self._solve_circuit_matrix(circuit_matrix)

            # Calculate component currents and voltages from node voltages
            for component in self.components.values():
                component_result = self._calculate_component_values(component, node_voltages)
                if component_result:
                    results[component.id] = component_result
        else:
            # Fallback to basic analysis
            for component in self.components.values():
                if component.type == 'battery':
                    results[component.id] = {
                        'voltage': component.value,
                        'current': 0.1,  # Placeholder
                        'power': component.value * 0.1
                    }

        return results

    def _build_circuit_matrix(self):
        """Build circuit matrix for nodal analysis"""
        # This is a simplified implementation
        # In a full implementation, this would create a proper admittance matrix

        # Identify unique nodes
        nodes = set()
        for wire in self.wires:
            nodes.add(wire.get('from_node', 'node_0'))
            nodes.add(wire.get('to_node', 'node_1'))

        if len(nodes) < 2:
            return None

        # Create simplified matrix representation
        matrix = {
            'nodes': list(nodes),
            'components': list(self.components.values()),
            'connections': self.wires
        }

        return matrix

    def _solve_circuit_matrix(self, matrix):
        """Solve circuit matrix to find node voltages"""
        # Simplified solver - in reality would use numpy or similar
        node_voltages = {}

        # Find voltage sources and set reference
        voltage_sources = [c for c in matrix['components'] if c.type == 'battery']

        if voltage_sources:
            # Set ground reference
            node_voltages['node_0'] = 0

            # Simple voltage divider approximation for demonstration
            total_voltage = sum(v.value for v in voltage_sources)
            node_count = len(matrix['nodes'])

            for i, node in enumerate(matrix['nodes']):
                if node != 'node_0':
                    node_voltages[node] = total_voltage * (i / node_count)

        return node_voltages

    def _calculate_component_values(self, component, node_voltages):
        """Calculate component voltage and current from node voltages"""
        # Simplified calculation
        if component.type == 'resistor':
            # Assume voltage across resistor
            voltage = node_voltages.get('node_1', 0) - node_voltages.get('node_0', 0)
            current = voltage / component.value if component.value > 0 else 0

            return {
                'voltage': abs(voltage),
                'current': abs(current),
                'power': abs(voltage * current),
                'resistance': component.value
            }
        elif component.type == 'battery':
            return {
                'voltage': component.value,
                'current': 0.1,  # Simplified
                'power': component.value * 0.1
            }

        return None

    def ac_analysis(self, frequency: float = 1000):
        """Perform AC analysis at given frequency"""
        omega = 2 * math.pi * frequency
        results = {}

        for component in self.components.values():
            if component.type == 'capacitor':
                # Capacitive reactance: Xc = 1/(2πfC)
                capacitance = component.value * 1e-6  # Convert μF to F
                reactance = 1 / (omega * capacitance)
                impedance = complex(0, -reactance)

                results[component.id] = {
                    'impedance_magnitude': abs(impedance),
                    'impedance_phase': math.degrees(cmath.phase(impedance)),
                    'reactance': reactance,
                    'frequency': frequency
                }
            elif component.type == 'inductor':
                # Inductive reactance: Xl = 2πfL
                inductance = component.value * 1e-3  # Convert mH to H
                reactance = omega * inductance
                impedance = complex(0, reactance)

                results[component.id] = {
                    'impedance_magnitude': abs(impedance),
                    'impedance_phase': math.degrees(cmath.phase(impedance)),
                    'reactance': reactance,
                    'frequency': frequency
                }
            elif component.type == 'resistor':
                # Resistor impedance is purely real
                impedance = complex(component.value, 0)
                results[component.id] = {
                    'impedance_magnitude': abs(impedance),
                    'impedance_phase': 0,
                    'resistance': component.value,
                    'frequency': frequency
                }

        return results

    def transient_analysis(self, time_points: List[float]):
        """Perform transient analysis for RC/RL circuits"""
        results = {}

        # Find RC or RL time constants
        resistors = [c for c in self.components.values() if c.type == 'resistor']
        capacitors = [c for c in self.components.values() if c.type == 'capacitor']
        inductors = [c for c in self.components.values() if c.type == 'inductor']

        if resistors and capacitors:
            # RC circuit analysis
            R = sum(r.value for r in resistors)
            C = sum(c.value for c in capacitors) * 1e-6  # Convert to Farads
            tau = R * C  # Time constant

            for t in time_points:
                voltage_cap = 5 * (1 - math.exp(-t / tau))  # Assuming 5V step input
                current = (5 / R) * math.exp(-t / tau)

                results[f't_{t}'] = {
                    'time': t,
                    'capacitor_voltage': voltage_cap,
                    'current': current,
                    'time_constant': tau
                }

        return results

    def power_analysis(self):
        """Calculate power consumption and efficiency"""
        total_power_consumed = 0
        total_power_supplied = 0
        results = {}

        simulation_results = self.simulate()

        for component_id, data in simulation_results.items():
            component = self.components.get(component_id)
            if component:
                power = data.get('power', 0)

                if component.type == 'battery':
                    total_power_supplied += power
                else:
                    total_power_consumed += power

                # Calculate power efficiency for each component
                if component.type == 'led' and power > 0:
                    # LED efficiency (lumens per watt - simplified)
                    luminous_efficacy = 100  # lumens/watt for white LED
                    light_output = power * luminous_efficacy
                    results[component_id] = {
                        'power_consumed': power,
                        'light_output_lumens': light_output,
                        'efficiency': 'high' if power < 0.1 else 'medium'
                    }

        efficiency = (total_power_consumed / total_power_supplied * 100) if total_power_supplied > 0 else 0

        results['circuit_summary'] = {
            'total_power_supplied': total_power_supplied,
            'total_power_consumed': total_power_consumed,
            'efficiency_percent': efficiency,
            'power_loss': total_power_supplied - total_power_consumed
        }

        return results

# Global circuit instance
current_circuit = Circuit()

@app.route('/api/components', methods=['GET'])
def get_component_library():
    """Return available electronic components"""
    components = {
        'resistor': {
            'name': 'Resistor',
            'symbol': 'R',
            'unit': 'Ω',
            'values': [100, 220, 330, 470, 680, 1000, 1500, 2200, 3300, 4700, 6800, 10000, 22000, 47000, 100000],
            'tolerance': 0.05,  # 5% tolerance
            'power_rating': [0.125, 0.25, 0.5, 1.0, 2.0],  # Watts
            'description': 'Limits current flow and creates voltage drops',
            'color_codes': {
                100: ['brown', 'black', 'brown'],
                220: ['red', 'red', 'brown'],
                470: ['yellow', 'violet', 'brown'],
                1000: ['brown', 'black', 'red']
            }
        },
        'capacitor': {
            'name': 'Capacitor',
            'symbol': 'C',
            'unit': 'μF',
            'values': [0.1, 1, 4.7, 10, 22, 47, 100, 220, 470, 1000, 2200, 4700],
            'voltage_rating': [6.3, 10, 16, 25, 35, 50, 63, 100],
            'types': ['ceramic', 'electrolytic', 'tantalum', 'film'],
            'description': 'Stores electrical energy and filters signals',
            'polarity': {'electrolytic': True, 'tantalum': True, 'ceramic': False, 'film': False}
        },
        'led': {
            'name': 'LED',
            'symbol': 'LED',
            'unit': '',
            'colors': ['red', 'green', 'blue', 'yellow', 'white', 'orange', 'purple'],
            'forward_voltage': {'red': 1.8, 'green': 2.1, 'blue': 3.2, 'yellow': 2.0, 'white': 3.2, 'orange': 2.0, 'purple': 3.2},
            'max_current': 0.02,  # 20mA
            'description': 'Light Emitting Diode - converts electrical energy to light',
            'viewing_angle': 120  # degrees
        },
        'battery': {
            'name': 'Battery',
            'symbol': 'V',
            'unit': 'V',
            'values': [1.5, 3, 3.7, 5, 6, 9, 12, 24],
            'types': ['alkaline', 'lithium', 'nimh', 'lead_acid'],
            'capacity': {'1.5': 2500, '3': 1000, '9': 500, '12': 7000},  # mAh
            'description': 'Provides electrical energy to power circuits',
            'internal_resistance': 0.1  # Ohms
        },
        'switch': {
            'name': 'Switch',
            'symbol': 'SW',
            'unit': '',
            'types': ['spst', 'spdt', 'dpst', 'dpdt', 'momentary', 'toggle'],
            'states': ['open', 'closed'],
            'contact_resistance': 0.01,  # Ohms when closed
            'description': 'Controls circuit connection and current flow'
        },
        'diode': {
            'name': 'Diode',
            'symbol': 'D',
            'unit': '',
            'types': ['silicon', 'germanium', 'schottky', 'zener'],
            'forward_voltage': {'silicon': 0.7, 'germanium': 0.3, 'schottky': 0.2},
            'max_current': [0.1, 1, 3, 10],  # Amperes
            'description': 'Allows current flow in one direction only'
        },
        'transistor': {
            'name': 'Transistor',
            'symbol': 'Q',
            'unit': '',
            'types': ['npn', 'pnp', 'mosfet_n', 'mosfet_p'],
            'beta': [50, 100, 200, 300],  # Current gain
            'max_collector_current': [0.1, 0.5, 1, 2],  # Amperes
            'description': 'Amplifies signals or acts as an electronic switch'
        },
        'inductor': {
            'name': 'Inductor',
            'symbol': 'L',
            'unit': 'mH',
            'values': [1, 10, 47, 100, 220, 470, 1000],
            'current_rating': [0.1, 0.5, 1, 2, 5],  # Amperes
            'description': 'Stores energy in magnetic field, opposes current changes'
        },
        'potentiometer': {
            'name': 'Potentiometer',
            'symbol': 'POT',
            'unit': 'Ω',
            'values': [1000, 5000, 10000, 50000, 100000, 500000, 1000000],
            'types': ['linear', 'logarithmic'],
            'description': 'Variable resistor for adjusting voltage or current'
        },
        'wire': {
            'name': 'Wire',
            'symbol': '',
            'unit': '',
            'awg_sizes': [22, 20, 18, 16, 14, 12],
            'resistance_per_foot': {22: 0.0161, 20: 0.0101, 18: 0.00636},  # Ohms
            'current_capacity': {22: 0.92, 20: 1.5, 18: 2.3},  # Amperes
            'description': 'Connects components and carries electrical current'
        },
        'function_generator': {
            'name': 'Function Generator',
            'symbol': 'FG',
            'unit': 'Hz',
            'frequency_range': [0.1, 100000],  # 0.1 Hz to 100 kHz
            'amplitude_range': [0.1, 20],  # 0.1V to 20V peak-to-peak
            'waveforms': ['sine', 'square', 'triangle', 'sawtooth'],
            'description': 'Generates various AC waveforms for testing'
        },
        'signal_generator': {
            'name': 'Signal Generator',
            'symbol': 'SG',
            'unit': 'Hz',
            'frequency_range': [1, 1000000],  # 1 Hz to 1 MHz
            'amplitude_range': [0.01, 10],  # 10mV to 10V
            'modulation': ['AM', 'FM', 'PM'],
            'description': 'Precision signal source for measurements'
        },
        'pulse_generator': {
            'name': 'Pulse Generator',
            'symbol': 'PG',
            'unit': 'Hz',
            'frequency_range': [0.001, 50000],  # 1 mHz to 50 kHz
            'pulse_width': [1e-6, 1],  # 1 microsecond to 1 second
            'rise_time': [1e-9, 1e-3],  # 1 nanosecond to 1 millisecond
            'description': 'Generates precise pulse and square wave signals'
        },
        'oscilloscope_probe': {
            'name': 'Oscilloscope Probe',
            'symbol': 'PROBE',
            'unit': '',
            'attenuation': [1, 10, 100],  # 1x, 10x, 100x
            'bandwidth': [100e6, 500e6, 1e9],  # 100 MHz, 500 MHz, 1 GHz
            'input_impedance': 1e6,  # 1 MΩ
            'description': 'High-impedance probe for oscilloscope measurements'
        },
        'current_probe': {
            'name': 'Current Probe',
            'symbol': 'I-PROBE',
            'unit': 'A',
            'current_range': [1e-6, 100],  # 1 μA to 100 A
            'bandwidth': [1e6, 100e6],  # 1 MHz to 100 MHz
            'sensitivity': [1e-3, 1],  # 1 mV/A to 1 V/A
            'description': 'Non-invasive current measurement probe'
        },
        'test_point': {
            'name': 'Test Point',
            'symbol': 'TP',
            'unit': '',
            'contact_resistance': 0.001,  # 1 mΩ
            'current_rating': 5,  # 5 A
            'description': 'Convenient connection point for measurements'
        },
        'zener_diode': {
            'name': 'Zener Diode',
            'symbol': 'DZ',
            'unit': 'V',
            'zener_voltages': [3.3, 5.1, 6.2, 9.1, 12, 15, 18, 24],
            'power_ratings': [0.5, 1, 5, 10],  # Watts
            'tolerance': [2, 5, 10],  # Percent
            'description': 'Voltage regulation diode with precise breakdown voltage'
        },
        'tunnel_diode': {
            'name': 'Tunnel Diode',
            'symbol': 'TD',
            'unit': 'mA',
            'peak_currents': [1, 5, 10, 50, 100],  # mA
            'peak_voltage': 0.1,  # Typical peak voltage
            'valley_voltage': 0.35,  # Typical valley voltage
            'description': 'High-speed switching diode with negative resistance region'
        },
        'schottky_diode': {
            'name': 'Schottky Diode',
            'symbol': 'SD',
            'unit': 'A',
            'forward_voltage': 0.2,  # Lower than silicon diode
            'current_ratings': [0.1, 1, 3, 10, 30],  # Amperes
            'description': 'Fast switching diode with low forward voltage drop'
        },
        'photodiode': {
            'name': 'Photodiode',
            'symbol': 'PD',
            'unit': 'A',
            'dark_current': 1e-9,  # 1 nA typical
            'responsivity': 0.5,  # A/W at 850nm
            'wavelength_range': [400, 1100],  # nm
            'description': 'Light-sensitive diode for optical detection'
        },
        'avalanche_diode': {
            'name': 'Avalanche Photodiode',
            'symbol': 'APD',
            'unit': 'A',
            'gain': [10, 100, 1000],  # Multiplication factor
            'breakdown_voltage': [30, 100, 200],  # Volts
            'dark_current': 1e-12,  # 1 pA typical
            'description': 'High-gain photodiode with internal amplification'
        },
        'pin_diode': {
            'name': 'PIN Diode',
            'symbol': 'PIN',
            'unit': 'A',
            'forward_voltage': 0.7,
            'reverse_resistance': [1e6, 1e9],  # High reverse resistance
            'switching_speed': [1e-9, 1e-6],  # nanoseconds
            'description': 'Fast switching diode with intrinsic layer'
        },
        'varactor_diode': {
            'name': 'Varactor Diode',
            'symbol': 'VAR',
            'unit': 'pF',
            'capacitance_range': [1, 100],  # pF
            'voltage_range': [0, 30],  # Reverse bias voltage
            'q_factor': [50, 200],
            'description': 'Voltage-controlled capacitor diode'
        },
        'bjt_npn': {
            'name': 'NPN BJT',
            'symbol': 'Q',
            'unit': '',
            'beta_range': [50, 300],  # Current gain
            'max_collector_current': [0.1, 1, 5, 10],  # Amperes
            'max_voltage': [30, 60, 100, 200],  # Volts
            'description': 'NPN Bipolar Junction Transistor for amplification'
        },
        'bjt_pnp': {
            'name': 'PNP BJT',
            'symbol': 'Q',
            'unit': '',
            'beta_range': [50, 300],  # Current gain
            'max_collector_current': [0.1, 1, 5, 10],  # Amperes
            'max_voltage': [30, 60, 100, 200],  # Volts
            'description': 'PNP Bipolar Junction Transistor for amplification'
        },
        'mosfet_n': {
            'name': 'N-Channel MOSFET',
            'symbol': 'M',
            'unit': 'A',
            'drain_current': [0.1, 1, 10, 30, 100],  # Amperes
            'gate_threshold': [1, 2, 4],  # Volts
            'on_resistance': [0.01, 0.1, 1],  # Ohms
            'description': 'N-Channel MOSFET for switching and amplification'
        },
        'mosfet_p': {
            'name': 'P-Channel MOSFET',
            'symbol': 'M',
            'unit': 'A',
            'drain_current': [0.1, 1, 10, 30],  # Amperes
            'gate_threshold': [-1, -2, -4],  # Volts (negative for P-channel)
            'on_resistance': [0.01, 0.1, 1],  # Ohms
            'description': 'P-Channel MOSFET for switching and amplification'
        },
        'timer_555': {
            'name': '555 Timer',
            'symbol': '555',
            'unit': 'Hz',
            'frequency_range': [0.1, 100000],  # Hz
            'supply_voltage': [4.5, 16],  # Volts
            'output_current': 0.2,  # Amperes
            'description': 'Versatile timer IC for oscillators and pulse generation'
        },
        'voltage_regulator': {
            'name': 'Voltage Regulator',
            'symbol': 'REG',
            'unit': 'V',
            'output_voltages': [3.3, 5, 9, 12, 15],  # Volts
            'current_ratings': [0.1, 1, 3, 5],  # Amperes
            'dropout_voltage': [0.2, 1, 2],  # Volts
            'description': 'Provides stable output voltage from varying input'
        },
        'comparator': {
            'name': 'Comparator',
            'symbol': 'COMP',
            'unit': 'V',
            'input_offset': [0.001, 0.01],  # Volts
            'supply_voltage': [5, 15, 30],  # Volts
            'response_time': [10e-9, 100e-9],  # Seconds
            'description': 'Compares two input voltages and outputs digital result'
        },
        'igbt': {
            'name': 'IGBT',
            'symbol': 'IGBT',
            'unit': 'A',
            'collector_current': [1, 10, 50, 100, 600],  # Amperes
            'gate_threshold': [3, 5, 6],  # Volts
            'on_resistance': [0.001, 0.01, 0.1],  # Ohms
            'description': 'Insulated Gate Bipolar Transistor for high power switching'
        },
        'darlington_transistor': {
            'name': 'Darlington Transistor',
            'symbol': 'DARL',
            'unit': '',
            'beta': [1000, 5000, 10000],  # Very high current gain
            'max_collector_current': [0.5, 2, 5, 10],  # Amperes
            'saturation_voltage': [1.2, 1.5, 2.0],  # Higher than single BJT
            'description': 'High-gain transistor pair for amplification'
        },
        'unijunction_transistor': {
            'name': 'UJT',
            'symbol': 'UJT',
            'unit': 'V',
            'peak_voltage': [0.5, 1.0, 2.0],  # Volts
            'valley_voltage': [0.1, 0.3, 0.5],  # Volts
            'intrinsic_standoff_ratio': [0.5, 0.7, 0.8],
            'description': 'Unijunction transistor for oscillator circuits'
        },
        'spectrum_analyzer': {
            'name': 'Spectrum Analyzer',
            'symbol': 'SA',
            'unit': 'Hz',
            'frequency_range': [1e3, 1e9],  # 1 kHz to 1 GHz
            'resolution_bandwidth': [1, 100, 1000],  # Hz
            'dynamic_range': [60, 80, 100],  # dB
            'description': 'Analyzes frequency spectrum of signals'
        },
        'network_analyzer': {
            'name': 'Network Analyzer',
            'symbol': 'NA',
            'unit': 'Hz',
            'frequency_range': [1e6, 1e10],  # 1 MHz to 10 GHz
            's_parameters': ['S11', 'S12', 'S21', 'S22'],
            'dynamic_range': [80, 100, 120],  # dB
            'description': 'Measures network parameters and impedance'
        },
        'logic_analyzer': {
            'name': 'Logic Analyzer',
            'symbol': 'LA',
            'unit': 'channels',
            'channel_count': [8, 16, 32, 64, 128],
            'sample_rate': [1e6, 1e9, 10e9],  # Samples per second
            'memory_depth': [1e3, 1e6, 1e9],  # Samples
            'description': 'Captures and analyzes digital signals'
        },
        'power_meter': {
            'name': 'Power Meter',
            'symbol': 'PM',
            'unit': 'W',
            'power_range': [1e-6, 1e3],  # μW to kW
            'frequency_range': [1e3, 1e9],  # 1 kHz to 1 GHz
            'accuracy': [0.1, 1, 5],  # Percent
            'description': 'Measures electrical power consumption'
        }
    }
    return jsonify(components)

@app.route('/api/circuit', methods=['POST'])
def update_circuit():
    """Update the current circuit"""
    data = request.json
    
    # Clear current circuit
    current_circuit.components.clear()
    current_circuit.wires.clear()
    
    # Add components
    for comp_data in data.get('components', []):
        current_circuit.add_component(comp_data)
    
    # Add wires
    for wire_data in data.get('wires', []):
        current_circuit.add_wire(wire_data)
    
    return jsonify({"status": "success"})

@app.route('/api/simulate', methods=['POST'])
def simulate_circuit():
    """Simulate the current circuit"""
    results = current_circuit.simulate()
    return jsonify(results)

@app.route('/api/simulate/ac', methods=['POST'])
def simulate_ac():
    """Perform AC analysis"""
    data = request.json
    frequency = data.get('frequency', 1000)  # Default 1kHz
    results = current_circuit.ac_analysis(frequency)
    return jsonify(results)

@app.route('/api/simulate/transient', methods=['POST'])
def simulate_transient():
    """Perform transient analysis"""
    data = request.json
    time_points = data.get('time_points', [0, 0.001, 0.002, 0.005, 0.01, 0.02, 0.05])
    results = current_circuit.transient_analysis(time_points)
    return jsonify(results)

@app.route('/api/simulate/power', methods=['POST'])
def simulate_power():
    """Perform power analysis"""
    results = current_circuit.power_analysis()
    return jsonify(results)

@app.route('/api/simulate/frequency_sweep', methods=['POST'])
def frequency_sweep():
    """Perform frequency sweep analysis"""
    data = request.json
    start_freq = data.get('start_frequency', 10)
    end_freq = data.get('end_frequency', 100000)
    num_points = data.get('num_points', 50)

    frequencies = np.logspace(math.log10(start_freq), math.log10(end_freq), num_points)
    results = {}

    for freq in frequencies:
        ac_result = current_circuit.ac_analysis(freq)
        results[f'freq_{freq:.1f}'] = {
            'frequency': freq,
            'components': ac_result
        }

    return jsonify(results)

@app.route('/api/tutorials', methods=['GET'])
def get_tutorials():
    """Return available tutorials"""
    tutorials = [
        {
            'id': 'basic_led',
            'title': 'Basic LED Circuit',
            'description': 'Learn to light up an LED with a battery and resistor',
            'difficulty': 'beginner',
            'components': ['battery', 'resistor', 'led', 'wire'],
            'steps': [
                'Place a 9V battery on the breadboard',
                'Add a 470Ω resistor in series',
                'Connect an LED (mind the polarity!)',
                'Complete the circuit with wires',
                'Observe the LED lighting up'
            ]
        },
        {
            'id': 'voltage_divider',
            'title': 'Voltage Divider',
            'description': 'Create a voltage divider circuit',
            'difficulty': 'intermediate',
            'components': ['battery', 'resistor', 'wire'],
            'steps': [
                'Place two resistors in series',
                'Connect to a voltage source',
                'Measure voltage across each resistor',
                'Calculate the voltage division ratio'
            ]
        }
    ]
    return jsonify(tutorials)

@app.route('/api/tutorial/<tutorial_id>', methods=['GET'])
def get_tutorial(tutorial_id):
    """Get specific tutorial details"""
    # This would fetch from a database in a real app
    tutorials = {
        'basic_led': {
            'id': 'basic_led',
            'title': 'Basic LED Circuit',
            'description': 'Learn to light up an LED with a battery and resistor',
            'circuit': {
                'components': [
                    {'id': 'bat1', 'type': 'battery', 'value': 9, 'position': {'x': 100, 'y': 100}},
                    {'id': 'res1', 'type': 'resistor', 'value': 470, 'position': {'x': 200, 'y': 100}},
                    {'id': 'led1', 'type': 'led', 'color': 'red', 'position': {'x': 300, 'y': 100}}
                ],
                'wires': [
                    {'from': 'bat1_pos', 'to': 'res1_1'},
                    {'from': 'res1_2', 'to': 'led1_anode'},
                    {'from': 'led1_cathode', 'to': 'bat1_neg'}
                ]
            }
        }
    }

    if tutorial_id in tutorials:
        return jsonify(tutorials[tutorial_id])
    else:
        return jsonify({"error": "Tutorial not found"}), 404

@app.route('/api/electron_flow', methods=['POST'])
def get_electron_flow_data():
    """Get detailed electron flow data for enhanced visualization"""
    results = current_circuit.simulate()

    # Calculate electron flow for each wire
    electron_flow_data = {}

    for wire_id, wire in enumerate(current_circuit.wires):
        # Find connected components
        connected_components = []
        for component in current_circuit.components.values():
            # Simplified connection check
            connected_components.append(component)

        # Calculate flow parameters
        if connected_components:
            # Get current from simulation results
            total_current = 0
            total_voltage = 0

            for component in connected_components:
                comp_result = results.get(component.id, {})
                current = comp_result.get('current', 0)
                voltage = comp_result.get('voltage', 0)

                total_current += current
                total_voltage += voltage

            # Calculate electron flow characteristics
            electron_density = min(10, max(1, total_current * 1000))  # electrons per unit length
            electron_speed = min(5, max(0.1, total_current * 100))    # relative speed
            electron_energy = total_voltage * 0.1                      # energy level

            electron_flow_data[f'wire_{wire_id}'] = {
                'current': total_current,
                'voltage': total_voltage,
                'electron_density': electron_density,
                'electron_speed': electron_speed,
                'electron_energy': electron_energy,
                'flow_direction': 1 if total_current > 0 else -1
            }

    return jsonify({
        'simulation_results': results,
        'electron_flow': electron_flow_data,
        'timestamp': time.time()
    })

@app.route('/api/component_analysis/<component_id>', methods=['GET'])
def get_component_analysis(component_id):
    """Get detailed analysis for a specific component"""
    component = current_circuit.components.get(component_id)
    if not component:
        return jsonify({"error": "Component not found"}), 404

    # Run simulation to get current state
    results = current_circuit.simulate()
    component_result = results.get(component_id, {})

    # Add detailed analysis based on component type
    analysis = {
        'basic_parameters': component_result,
        'component_type': component.type,
        'component_value': component.value
    }

    if component.type == 'resistor':
        analysis['color_code'] = calculate_resistor_color_code(component.value)
        analysis['power_dissipation'] = component_result.get('power', 0)
        analysis['temperature_rise'] = component_result.get('power', 0) * 10  # Simplified

    elif component.type == 'led':
        analysis['luminous_intensity'] = component_result.get('brightness', 0) * 1000  # mcd
        analysis['forward_current'] = component_result.get('current', 0)
        analysis['efficiency'] = component_result.get('brightness', 0) * 100  # %

    elif component.type in ['tunnel_diode', 'zener_diode', 'schottky_diode']:
        analysis['diode_characteristics'] = {
            'forward_biased': component_result.get('conducting', False),
            'breakdown_voltage': getattr(component, 'zener_voltage', None),
            'switching_speed': getattr(component, 'switching_speed', None)
        }

    return jsonify(analysis)

def calculate_resistor_color_code(resistance):
    """Calculate resistor color code bands"""
    # Simplified color code calculation
    colors = ['black', 'brown', 'red', 'orange', 'yellow', 'green', 'blue', 'violet', 'gray', 'white']

    if resistance < 10:
        return ['brown', 'black', 'gold']  # < 10 ohm
    elif resistance < 100:
        first = int(resistance / 10)
        second = int(resistance % 10)
        return [colors[first], colors[second], 'black']
    else:
        # Standard 3-band calculation
        magnitude = 0
        value = resistance
        while value >= 100:
            value /= 10
            magnitude += 1

        first = int(value / 10)
        second = int(value % 10)

        return [colors[first], colors[second], colors[magnitude]]

if __name__ == '__main__':
    app.run(debug=True, port=5000)
