# Neighborhood Watch Image Enhancement Tool

A privacy-conscious web application designed to enhance low-quality security footage for neighborhood watch groups and local communities.

## Features

- Upload and enhance security camera footage
- Automatically enhance faces without facial recognition
- Upscale image quality with Real-ESRGAN
- Denoise and improve lighting
- Add optional metadata (location, date, time)
- Privacy-focused: no data storage, no identity matching

## Setup Instructions

### Prerequisites

- Python 3.7 or higher
- CUDA-compatible GPU recommended for faster processing

### Installation

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/neighborhood-watch-enhancer.git
   cd neighborhood-watch-enhancer
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Run the application:
   ```
   python app.py
   ```

5. Open your browser and navigate to the URL shown in the terminal (typically http://127.0.0.1:7860)

## Deployment Options

### Hugging Face Spaces

1. Create a new Space on Hugging Face
2. Upload the code to the Space
3. Add the requirements.txt file
4. The Space will automatically build and deploy the application

### Docker Deployment

A Dockerfile is provided for containerized deployment:

```
docker build -t neighborhood-watch-enhancer .
docker run -p 7860:7860 neighborhood-watch-enhancer
```

## Privacy and Ethics

This tool is designed with privacy and ethics in mind:

- No facial recognition or identity matching
- No permanent storage of images or user data
- Processing happens locally or in short-lived sessions
- Includes disclaimers against misuse

## License

MIT License