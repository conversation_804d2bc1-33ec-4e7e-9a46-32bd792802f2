@echo off
echo 🔌 Electronics Practice Simulator
echo ========================================
echo Starting the Electronics Practice Simulator...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python not found. Please install Python 3.6 or higher.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✓ Python found

REM Install backend dependencies
echo Installing backend dependencies...
cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo ✗ Failed to install dependencies
    pause
    exit /b 1
)
echo ✓ Dependencies installed

REM Start backend server
echo Starting backend server...
start "Electronics Simulator Backend" python app.py

REM Wait for backend to start
timeout /t 3 /nobreak >nul

REM Start frontend server
echo Starting frontend server...
cd ..\frontend
start "Electronics Simulator Frontend" python -m http.server 8000

REM Wait for frontend to start
timeout /t 2 /nobreak >nul

REM Open browser
echo Opening browser...
start http://localhost:8000

echo.
echo ========================================
echo 🎉 Electronics Simulator is running!
echo Backend:  http://localhost:5000
echo Frontend: http://localhost:8000
echo ========================================
echo.
echo Press any key to stop the servers...
pause >nul

REM Stop servers (this is basic - servers will continue running)
echo Servers are still running in separate windows.
echo Close the command windows to stop them.
pause
