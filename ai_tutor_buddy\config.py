import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# API keys and configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Voice settings
VOICE_RATE = 180  # Words per minute
VOICE_VOLUME = 1.0  # Volume (0.0 to 1.0)

# Memory settings
MEMORY_FILE = "memory/user_data.json"
VECTOR_DB_PATH = "memory/vectordb"

# User preferences (defaults)
DEFAULT_USER = {
    "name": "Student",
    "goals": ["Learn new concepts", "Improve understanding"],
    "subjects": ["General knowledge"],
    "learning_style": "Conversational"
}