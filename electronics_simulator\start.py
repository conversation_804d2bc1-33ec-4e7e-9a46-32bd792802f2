#!/usr/bin/env python3
"""
Electronics Simulator Startup Script
Starts both backend and frontend servers
"""

import subprocess
import sys
import os
import time
import webbrowser
from pathlib import Path

def check_python():
    """Check if Python is available"""
    try:
        result = subprocess.run([sys.executable, '--version'], 
                              capture_output=True, text=True)
        print(f"✓ Python found: {result.stdout.strip()}")
        return True
    except:
        print("✗ Python not found")
        return False

def install_backend_deps():
    """Install backend dependencies"""
    print("Installing backend dependencies...")
    backend_dir = Path(__file__).parent / "backend"
    requirements_file = backend_dir / "requirements.txt"
    
    if requirements_file.exists():
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 
                          str(requirements_file)], check=True)
            print("✓ Backend dependencies installed")
            return True
        except subprocess.CalledProcessError:
            print("✗ Failed to install backend dependencies")
            return False
    else:
        print("✗ requirements.txt not found")
        return False

def start_backend():
    """Start the Flask backend server"""
    print("Starting backend server...")
    backend_dir = Path(__file__).parent / "backend"
    app_file = backend_dir / "app.py"
    
    if app_file.exists():
        try:
            # Start backend in a new process
            process = subprocess.Popen([sys.executable, str(app_file)], 
                                     cwd=str(backend_dir))
            print("✓ Backend server starting on http://localhost:5000")
            return process
        except Exception as e:
            print(f"✗ Failed to start backend: {e}")
            return None
    else:
        print("✗ Backend app.py not found")
        return None

def start_frontend():
    """Start the frontend server"""
    print("Starting frontend server...")
    frontend_dir = Path(__file__).parent / "frontend"
    index_file = frontend_dir / "index.html"
    
    if index_file.exists():
        try:
            # Start a simple HTTP server for the frontend
            process = subprocess.Popen([sys.executable, '-m', 'http.server', '8000'], 
                                     cwd=str(frontend_dir))
            print("✓ Frontend server starting on http://localhost:8000")
            return process
        except Exception as e:
            print(f"✗ Failed to start frontend: {e}")
            return None
    else:
        print("✗ Frontend index.html not found")
        return None

def open_browser():
    """Open the application in the default browser"""
    print("Opening browser...")
    try:
        webbrowser.open('http://localhost:8000')
        print("✓ Browser opened")
    except Exception as e:
        print(f"✗ Failed to open browser: {e}")
        print("Please manually open http://localhost:8000 in your browser")

def main():
    """Main startup function"""
    print("🔌 Electronics Practice Simulator")
    print("=" * 40)
    
    # Check Python
    if not check_python():
        print("Please install Python 3.6 or higher")
        return
    
    # Install dependencies
    if not install_backend_deps():
        print("Please install dependencies manually:")
        print("cd backend && pip install -r requirements.txt")
        return
    
    # Start backend
    backend_process = start_backend()
    if not backend_process:
        return
    
    # Wait a moment for backend to start
    print("Waiting for backend to initialize...")
    time.sleep(3)
    
    # Start frontend
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        return
    
    # Wait a moment for frontend to start
    time.sleep(2)
    
    # Open browser
    open_browser()
    
    print("\n" + "=" * 40)
    print("🎉 Electronics Simulator is running!")
    print("Backend:  http://localhost:5000")
    print("Frontend: http://localhost:8000")
    print("=" * 40)
    print("\nPress Ctrl+C to stop both servers")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\nShutting down servers...")
        backend_process.terminate()
        frontend_process.terminate()
        print("✓ Servers stopped")

if __name__ == "__main__":
    main()
