#!/usr/bin/env python3
"""
Test Runner for Electronics Practice Simulator
Runs all tests and generates a comprehensive report
"""

import unittest
import sys
import os
import time
from io import StringIO

def run_all_tests():
    """Run all tests and generate a report"""
    print("🧪 Electronics Practice Simulator - Test Suite")
    print("=" * 50)
    
    # Add backend directory to path
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    sys.path.insert(0, backend_dir)
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = backend_dir
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Create test runner with detailed output
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        descriptions=True,
        failfast=False
    )
    
    # Run tests and capture results
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Print results
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"Time: {end_time - start_time:.2f} seconds")
    print()
    
    # Print detailed output
    output = stream.getvalue()
    print("Detailed Test Output:")
    print("-" * 30)
    print(output)
    
    # Print failures and errors if any
    if result.failures:
        print("\n❌ FAILURES:")
        print("-" * 20)
        for test, traceback in result.failures:
            print(f"FAIL: {test}")
            print(traceback)
            print()
    
    if result.errors:
        print("\n💥 ERRORS:")
        print("-" * 20)
        for test, traceback in result.errors:
            print(f"ERROR: {test}")
            print(traceback)
            print()
    
    # Overall result
    if result.wasSuccessful():
        print("✅ ALL TESTS PASSED!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking Dependencies...")
    
    required_packages = [
        'flask',
        'flask_cors',
        'numpy',
        'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r backend/requirements.txt")
        return False
    else:
        print("✅ All dependencies satisfied")
        return True

def test_api_endpoints():
    """Test API endpoints if server is running"""
    print("\n🌐 Testing API Endpoints...")
    
    try:
        import requests
        base_url = "http://localhost:5000/api"
        
        # Test component library endpoint
        try:
            response = requests.get(f"{base_url}/components", timeout=5)
            if response.status_code == 200:
                print("✓ Component library endpoint")
            else:
                print(f"✗ Component library endpoint - Status: {response.status_code}")
        except requests.exceptions.RequestException:
            print("✗ Component library endpoint - Server not running")
        
        # Test tutorials endpoint
        try:
            response = requests.get(f"{base_url}/tutorials", timeout=5)
            if response.status_code == 200:
                print("✓ Tutorials endpoint")
            else:
                print(f"✗ Tutorials endpoint - Status: {response.status_code}")
        except requests.exceptions.RequestException:
            print("✗ Tutorials endpoint - Server not running")
            
    except ImportError:
        print("⚠️  requests package not available - skipping API tests")
        print("Install with: pip install requests")

def validate_frontend_files():
    """Validate that frontend files exist and are properly structured"""
    print("\n📁 Validating Frontend Files...")
    
    frontend_dir = os.path.join(os.path.dirname(__file__), 'frontend')
    required_files = [
        'index.html',
        'app.js'
    ]
    
    all_files_exist = True
    
    for file in required_files:
        file_path = os.path.join(frontend_dir, file)
        if os.path.exists(file_path):
            print(f"✓ {file}")
            
            # Basic content validation
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if file == 'index.html':
                if 'Electronics Practice Simulator' in content:
                    print(f"  ✓ {file} contains expected title")
                else:
                    print(f"  ⚠️  {file} missing expected title")
                    
            elif file == 'app.js':
                if 'simulateCircuit' in content:
                    print(f"  ✓ {file} contains simulation functions")
                else:
                    print(f"  ⚠️  {file} missing simulation functions")
        else:
            print(f"✗ {file} - MISSING")
            all_files_exist = False
    
    return all_files_exist

def generate_test_report():
    """Generate a comprehensive test report"""
    print("\n📊 Generating Test Report...")
    
    report_content = f"""
# Electronics Practice Simulator - Test Report
Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

## Test Summary
- Backend unit tests: {'PASSED' if run_all_tests() else 'FAILED'}
- Frontend file validation: {'PASSED' if validate_frontend_files() else 'FAILED'}
- Dependency check: {'PASSED' if check_dependencies() else 'FAILED'}

## Component Coverage
- Circuit simulation engine
- Component library management
- API endpoint functionality
- Error handling
- AC/DC analysis
- Power calculations

## Recommendations
1. Run tests before making changes
2. Test with different browsers
3. Verify all tutorials work correctly
4. Check performance with large circuits
5. Validate educational content accuracy

## Next Steps
- Add integration tests
- Implement automated UI testing
- Add performance benchmarks
- Create more example circuits
"""
    
    report_file = os.path.join(os.path.dirname(__file__), 'test_report.md')
    with open(report_file, 'w') as f:
        f.write(report_content)
    
    print(f"✓ Test report saved to: {report_file}")

def main():
    """Main test runner function"""
    print("🚀 Starting Electronics Simulator Test Suite")
    print("=" * 60)
    
    # Check dependencies first
    deps_ok = check_dependencies()
    
    # Validate frontend files
    frontend_ok = validate_frontend_files()
    
    # Run backend tests
    if deps_ok:
        tests_ok = run_all_tests()
    else:
        print("⚠️  Skipping tests due to missing dependencies")
        tests_ok = False
    
    # Test API endpoints (optional)
    test_api_endpoints()
    
    # Generate report
    generate_test_report()
    
    # Final summary
    print("\n" + "=" * 60)
    print("🏁 Test Suite Complete")
    
    if deps_ok and frontend_ok and tests_ok:
        print("✅ All checks passed - Ready for use!")
        return 0
    else:
        print("❌ Some checks failed - Review output above")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
