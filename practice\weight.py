
weight = float(input('How much do you weight on earth: '))



planet= int(input('What planet:\n1: Mercury\n2: Venus\n3: Mars\n4: Jupiter\n5: Saturn\n6: Uranus\n7: Neptune\n'))
  
if planet == 1:
  destination_weight = weight * .38
  print(f'Weight on Mercury is {destination_weight}lbs.')
elif planet == 2:
 destination_weight = weight * .91
 print(f'Weight on Venus is {destination_weight}lbs.')
elif planet == 3:
 destination_weight = weight * .38
 print(f'Weight on Mars is {destination_weight}lbs.')
elif planet == 4:
  destination_weight = weight * 2.53
  print(f'Weight on Jupiter is {destination_weight}lbs.')
elif planet == 5:
  destination_weight = weight * 1.07
  print(f'Weight on Saturn is {destination_weight}lbs.')
elif planet == 6:
 destination_weight = weight * .89
 print(f'Weight on Uranus is {destination_weight}lbs.')
elif planet == 7:
  destination_weight = weight * 1.14
  print(f'Weight on Neptune is {destination_weight}lbs.')
else:
  print('Invalid planet number.')

