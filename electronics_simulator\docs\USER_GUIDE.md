# Electronics Practice Simulator - User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Basic Operations](#basic-operations)
3. [Components Guide](#components-guide)
4. [Circuit Building](#circuit-building)
5. [Simulation Features](#simulation-features)
6. [Advanced Tools](#advanced-tools)
7. [Tutorials](#tutorials)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### First Launch
1. Start the application using `start.py` or `start.bat`
2. Open your browser to `http://localhost:8000`
3. You'll see the main interface with:
   - **Sidebar**: Component library
   - **Toolbar**: Simulation controls
   - **Workspace**: Circuit building area

### Interface Overview
- **Component Palette** (left): Drag components from here
- **Breadboard** (center): Drop and connect components
- **Toolbar** (top): Simulation and tool buttons
- **Results Panel** (right): Shows simulation data when active

## Basic Operations

### Adding Components
1. **Drag and Drop**: Drag any component from the sidebar to the workspace
2. **Positioning**: Components snap to a grid for neat alignment
3. **Selection**: Click on components to select them

### Making Connections
1. **Connection Points**: Red dots appear on components
2. **Wire Creation**: Click one connection point, then click another
3. **Wire Management**: Right-click wires to delete them

### Running Simulations
1. **Basic Simulation**: Click "▶️ Simulate" to analyze your circuit
2. **View Results**: Check the results panel for voltage, current, and power data
3. **Visual Feedback**: LEDs will light up based on current flow

## Components Guide

### Power Sources
- **9V Battery**: Standard 9-volt battery
- **5V Battery**: Lower voltage source
- **3V Battery**: For low-power circuits

**Usage Tips**:
- Always include at least one power source
- Check polarity when connecting
- Consider voltage ratings of other components

### Resistors
- **Values**: 220Ω, 470Ω, 1kΩ, 2.2kΩ, 4.7kΩ, 10kΩ
- **Purpose**: Current limiting, voltage division
- **Color Codes**: Displayed for educational value

**Calculations**:
- Voltage drop: V = I × R
- Power dissipation: P = I² × R
- Current limiting for LEDs: R = (Vsupply - VLED) / ILED

### LEDs (Light Emitting Diodes)
- **Colors**: Red, Green, Blue, Yellow, White
- **Forward Voltages**: 
  - Red: 1.8V
  - Green: 2.1V
  - Blue/White: 3.2V
  - Yellow: 2.0V
- **Maximum Current**: 20mA

**Important Notes**:
- LEDs are polarized (anode = +, cathode = -)
- Always use current-limiting resistors
- Brightness depends on current flow

### Capacitors
- **Values**: 1μF to 4700μF
- **Types**: Ceramic, electrolytic, tantalum
- **Voltage Ratings**: 6.3V to 100V

**Applications**:
- Energy storage
- Filtering
- Timing circuits (with resistors)

### Advanced Components
- **Diodes**: One-way current flow
- **Transistors**: Amplification and switching
- **Inductors**: Energy storage in magnetic fields
- **Potentiometers**: Variable resistance

## Circuit Building

### Basic LED Circuit
1. Add a 9V battery
2. Add a 470Ω resistor
3. Add a red LED
4. Connect: Battery(+) → Resistor → LED(+) → LED(-) → Battery(-)
5. Simulate to see the LED light up

### Voltage Divider
1. Add a 12V battery
2. Add two resistors (1kΩ and 2.2kΩ)
3. Connect in series: Battery(+) → R1 → R2 → Battery(-)
4. Measure voltage across R2 (should be ~8.25V)

### Parallel Resistors
1. Add a 9V battery
2. Add two resistors
3. Connect both resistors across the battery terminals
4. Observe how current divides between branches

## Simulation Features

### DC Analysis
- **Voltage Measurements**: Shows voltage across each component
- **Current Calculations**: Displays current through components
- **Power Analysis**: Calculates power consumption
- **Ohm's Law**: Automatically applies V = I × R

### AC Analysis
1. Click "📊 Oscilloscope" to open the AC analysis tool
2. Set frequency using the controls
3. View impedance and phase information for reactive components
4. Observe waveforms on the virtual oscilloscope

### Transient Analysis
- **RC Circuits**: Shows capacitor charging/discharging
- **Time Constants**: Calculates τ = R × C
- **Exponential Behavior**: Visualizes charging curves

## Advanced Tools

### Virtual Oscilloscope
- **Purpose**: Visualize AC waveforms and transient behavior
- **Controls**:
  - Time/Div: Adjusts horizontal time scale
  - Voltage/Div: Adjusts vertical voltage scale
  - Start/Stop: Controls waveform capture
- **Features**:
  - Grid display for measurements
  - Real-time waveform updates
  - Multiple time scales

### Digital Multimeter
- **Voltage Mode**: Measures DC voltage across components
- **Current Mode**: Shows current through components (in mA)
- **Resistance Mode**: Displays component resistance values
- **Usage**:
  1. Select measurement mode (V, A, or Ω)
  2. Choose component to probe
  3. Read measurement on digital display

### Circuit Save/Load
- **Saving**: 
  1. Click "💾 Save"
  2. Enter a circuit name
  3. Circuit is saved to browser local storage
- **Loading**:
  1. Click "📁 Load"
  2. Select from saved circuits
  3. Circuit loads automatically

## Tutorials

### Available Tutorials
1. **Basic LED Circuit**: Learn current limiting and LED polarity
2. **Voltage Divider**: Understand voltage division principles
3. **RC Circuits**: Explore capacitor charging behavior

### Tutorial Features
- **Step-by-step Instructions**: Clear, numbered steps
- **Component Explanations**: Why each component is needed
- **Expected Results**: What you should observe
- **Learning Objectives**: What concepts you'll master

### Using Tutorials
1. Click "📚 Tutorials" in the toolbar
2. Select a tutorial from the list
3. Follow the step-by-step instructions
4. Build the circuit as described
5. Run simulations to verify results

## Troubleshooting

### Common Issues

#### "No voltage source found" Error
- **Cause**: Circuit has no battery or power source
- **Solution**: Add a battery to your circuit

#### LED Not Lighting Up
- **Check Polarity**: Ensure anode (+) connects to positive voltage
- **Check Current**: Verify current is above 1mA threshold
- **Check Connections**: Ensure complete circuit path

#### Incorrect Voltage Readings
- **Verify Connections**: Check all wire connections
- **Component Values**: Confirm resistor values are correct
- **Circuit Topology**: Ensure series/parallel connections are as intended

#### Simulation Not Running
- **Backend Connection**: Ensure backend server is running on port 5000
- **Browser Console**: Check for JavaScript errors
- **Network Issues**: Verify localhost connectivity

### Performance Tips
- **Component Limit**: Keep circuits under 20 components for best performance
- **Wire Management**: Delete unused wires to reduce complexity
- **Browser Memory**: Refresh page if simulation becomes slow

### Getting Help
- **Built-in Examples**: Use example circuits as starting points
- **Tutorial Mode**: Follow guided tutorials for learning
- **Component Library**: Reference component specifications
- **Simulation Results**: Use measurement tools to verify behavior

## Best Practices

### Circuit Design
1. **Start Simple**: Begin with basic circuits before adding complexity
2. **Check Polarity**: Always verify LED and battery polarity
3. **Use Appropriate Values**: Choose resistor values for safe LED current
4. **Test Incrementally**: Add components one at a time and test

### Learning Approach
1. **Follow Tutorials**: Complete built-in tutorials first
2. **Experiment**: Try different component values
3. **Measure Everything**: Use the multimeter to verify calculations
4. **Save Progress**: Save interesting circuits for future reference

### Safety Considerations
- **Current Limits**: Keep LED current under 20mA
- **Power Ratings**: Ensure resistors can handle calculated power
- **Voltage Ratings**: Don't exceed component voltage ratings
- **Real-world Application**: Remember this is a simplified simulation

## Advanced Features

### Frequency Analysis
- Use the frequency sweep feature to analyze filter circuits
- Observe how capacitors and inductors behave at different frequencies
- Study resonance in LC circuits

### Power Efficiency
- Monitor power consumption in the results panel
- Calculate efficiency for different circuit topologies
- Optimize circuits for minimum power consumption

### Component Modeling
- Components include realistic parameters (tolerance, power ratings)
- Temperature effects are simplified but educational
- Parasitic effects are modeled for advanced learning

This simulator provides a safe, educational environment to learn electronics fundamentals. Experiment freely and use the measurement tools to verify your understanding!
