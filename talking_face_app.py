import tkinter as tk
from tkinter import ttk
import threading
import time

class TalkingFaceApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Talking Digital Face")
        self.root.geometry("600x500")
        self.root.configure(bg="#f0f0f0")
        
        # Backend - prompt processing
        self.current_prompt = ""
        self.is_talking = False
        self.talk_thread = None
        
        # Create UI elements
        self.create_widgets()
    
    def create_widgets(self):
        # Input frame
        input_frame = ttk.Frame(self.root, padding="10")
        input_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(input_frame, text="Enter prompt:").pack(side=tk.LEFT, padx=(0, 10))
        
        self.prompt_entry = ttk.Entry(input_frame, width=40)
        self.prompt_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.prompt_entry.bind("<Return>", self.process_prompt)
        
        ttk.Button(input_frame, text="Speak", command=self.process_prompt).pack(side=tk.LEFT, padx=(10, 0))
        
        # Face display area
        self.face_frame = ttk.Frame(self.root, padding="10")
        self.face_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Create face elements
        self.face_canvas = tk.Canvas(self.face_frame, bg="#ffffff", highlightthickness=0)
        self.face_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Draw initial face
        self.draw_face()
        
        # Text display area
        self.text_display = tk.Label(self.root, text="", font=("Arial", 14), 
                                     wraplength=500, bg="#f0f0f0", height=3)
        self.text_display.pack(fill=tk.X, padx=20, pady=10)
    
    def draw_face(self, mouth_open=False):
        self.face_canvas.delete("all")
        width = self.face_canvas.winfo_width()
        height = self.face_canvas.winfo_height()
        
        # If the canvas hasn't been drawn yet, use default values
        if width < 10:
            width = 500
            height = 300
        
        # Draw face circle
        face_radius = min(width, height) // 3
        face_x = width // 2
        face_y = height // 2
        self.face_canvas.create_oval(
            face_x - face_radius, face_y - face_radius,
            face_x + face_radius, face_y + face_radius,
            fill="#ffe0bd", outline="#000000", width=2
        )
        
        # Draw eyes
        eye_radius = face_radius // 5
        eye_y = face_y - face_radius // 3
        
        # Left eye
        left_eye_x = face_x - face_radius // 2
        self.face_canvas.create_oval(
            left_eye_x - eye_radius, eye_y - eye_radius,
            left_eye_x + eye_radius, eye_y + eye_radius,
            fill="white", outline="black", width=2
        )
        self.face_canvas.create_oval(
            left_eye_x - eye_radius//2, eye_y - eye_radius//2,
            left_eye_x + eye_radius//2, eye_y + eye_radius//2,
            fill="black"
        )
        
        # Right eye
        right_eye_x = face_x + face_radius // 2
        self.face_canvas.create_oval(
            right_eye_x - eye_radius, eye_y - eye_radius,
            right_eye_x + eye_radius, eye_y + eye_radius,
            fill="white", outline="black", width=2
        )
        self.face_canvas.create_oval(
            right_eye_x - eye_radius//2, eye_y - eye_radius//2,
            right_eye_x + eye_radius//2, eye_y + eye_radius//2,
            fill="black"
        )
        
        # Draw mouth
        mouth_y = face_y + face_radius // 2
        mouth_width = face_radius
        
        if mouth_open:
            # Open mouth
            self.face_canvas.create_oval(
                face_x - mouth_width//2, mouth_y - mouth_width//4,
                face_x + mouth_width//2, mouth_y + mouth_width//4,
                fill="black", outline="black"
            )
        else:
            # Closed mouth
            self.face_canvas.create_line(
                face_x - mouth_width//2, mouth_y,
                face_x + mouth_width//2, mouth_y,
                fill="black", width=3
            )
    
    # Backend methods
    def process_prompt(self, event=None):
        prompt = self.prompt_entry.get().strip()
        if not prompt:
            return
        
        self.current_prompt = prompt
        self.text_display.config(text="")
        self.prompt_entry.delete(0, tk.END)
        
        # Stop any existing talking
        if self.is_talking and self.talk_thread and self.talk_thread.is_alive():
            self.is_talking = False
            self.talk_thread.join()
        
        # Start new talking thread
        self.is_talking = True
        self.talk_thread = threading.Thread(target=self.animate_talking)
        self.talk_thread.daemon = True
        self.talk_thread.start()
    
    def animate_talking(self):
        displayed_text = ""
        
        for char in self.current_prompt:
            if not self.is_talking:
                break
                
            displayed_text += char
            self.text_display.config(text=displayed_text)
            
            # Animate mouth
            self.draw_face(mouth_open=True)
            self.root.update()
            time.sleep(0.1)
            
            self.draw_face(mouth_open=False)
            self.root.update()
            time.sleep(0.1)
        
        # Final state
        self.draw_face(mouth_open=False)
        self.is_talking = False

if __name__ == "__main__":
    root = tk.Tk()
    app = TalkingFaceApp(root)
    root.mainloop()