from voice_interface import VoiceInterface
from memory_system import MemorySystem
from ai_model import AIModel
import time
import os

class TutorBuddy:
    def __init__(self):
        print("Initializing AI Tutor Buddy...")
        self.voice = VoiceInterface()
        self.memory = MemorySystem()
        self.ai = AIModel()
        
    def start(self):
        """Start the AI Tutor Buddy"""
        # Welcome message
        user_name = self.memory.user_data["name"]
        welcome_message = f"Hello {user_name}! I'm your AI tutor buddy. How can I help you learn today?"
        self.voice.speak(welcome_message)
        
        # Main interaction loop
        while True:
            # Get user input (voice or text)
            user_input = self._get_user_input()
            
            if not user_input:
                continue
                
            # Check for exit command
            if user_input.lower() in ["exit", "quit", "goodbye", "bye"]:
                self.voice.speak(f"Goodbye, {user_name}! I hope we can continue learning together soon.")
                break
                
            # Check for special commands
            if self._handle_special_commands(user_input):
                continue
                
            # Search for relevant memories
            relevant_memories = self.memory.search_memories(user_input)
            
            # Generate AI response
            ai_response = self.ai.generate_response(
                user_input, 
                self.memory.user_data,
                self.memory.conversation_history,
                relevant_memories
            )
            
            # Speak the response
            self.voice.speak(ai_response)
            
            # Save the conversation
            self.memory.add_conversation(user_input, ai_response)
    
    def _get_user_input(self):
        """Get input from the user via voice or text"""
        try:
            # Try to get voice input
            user_input = self.voice.listen(timeout=5)
            
            # If voice input failed, fall back to text
            if not user_input:
                print("\nVoice input not detected. Please type your question:")
                user_input = input("> ")
                
            return user_input
        except Exception as e:
            print(f"Error getting user input: {e}")
            return None
    
    def _handle_special_commands(self, user_input):
        """Handle special commands like updating user info"""
        lower_input = user_input.lower()
        
        # Update name
        if "my name is" in lower_input or "call me" in lower_input:
            parts = user_input.split("is" if "my name is" in lower_input else "me", 1)
            if len(parts) > 1:
                name = parts[1].strip()
                self.memory.update_name(name)
                self.voice.speak(f"Great! I'll call you {name} from now on.")
                return True
        
        # Add subject
        if "i want to learn" in lower_input or "i'm studying" in lower_input:
            parts = user_input.split("learn" if "learn" in lower_input else "studying", 1)
            if len(parts) > 1:
                subject = parts[1].strip()
                self.memory.add_subject(subject)
                self.voice.speak(f"Excellent! I'll help you learn about {subject}.")
                return True
        
        # Add goal
        if "my goal is" in lower_input:
            parts = user_input.split("is", 1)
            if len(parts) > 1:
                goal = parts[1].strip()
                self.memory.add_goal(goal)
                self.voice.speak(f"I've added '{goal}' to your learning goals.")
                return True
                
        return False

if __name__ == "__main__":
    # Create necessary directories
    os.makedirs("memory", exist_ok=True)
    
    # Start the tutor buddy
    tutor = TutorBuddy()
    tutor.start()