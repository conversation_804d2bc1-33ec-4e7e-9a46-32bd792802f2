def build_periodic_table(filename="periodic_table.txt"):
    """Reads in the text file containing the periodic table.
    Returns a dictionary representing the periodic table.
    Each key is an atomic symbol, and the associated value is a tuple
    containing the element's name, atomic number, and atomic mass.
    For example, hydrogen would be stored in the dictionary
    with key 'H' and value ('Hydrogen', 1, 1.00794)
    """
    input_file = open(filename, 'r')  # Open the file containing the periodic table data
    table = dict()  # Initialize an empty dictionary to store the periodic table
    for element in input_file:
        tokens = element.split()  # Split each line into tokens
        table[tokens[2]] = (tokens[1], int(tokens[0]), float(tokens[3]))  # Map symbol to (name, atomic number, atomic mass)
    input_file.close()  # Close the file
    return table  # Return the dictionary


def run_program():
    periodic_table = build_periodic_table()  # Build the periodic table dictionary
    while True:
        # Display menu and get user choice
        u_num = int(input("1) Search by symbol/name\n2) Search by atomic mass\n3)"
                         " Molecular Mass Calculation\n4) Quit\n\nPlease enter your choice: "))

        # 1.) Search by symbol/name
        if u_num == 1:
            user_string_1 = str(input("Please enter your search string: "))  # Get search string from user
            # Find elements matching the search string in either symbol or name
            found_elements_set = {key for key, items in periodic_table.items()
                                  if user_string_1.lower() in key.lower() or user_string_1.lower() in items[0].lower()}

            found_elements_list = sorted(found_elements_set)  # Sort the found elements
            found_elements_dict = {i: periodic_table[i] for i in found_elements_list}  # Create a dictionary of found elements

            # Print header
            print()
            print(5 * " " + "#" + 3 * " " + "Sym" + 3 * " " + "Name" + 16 * " " + "Mass")
            print(55 * "=")

            # Print each found element's details
            for key, value in found_elements_dict.items():
                elt_num = str(value[1])
                elt_sym = str(key)
                elt_name = str(value[0])
                elt_mass = str(value[2])

                print((6 - len(str(elt_num))) * " " + str(elt_num) + 3 * " ", end="")
                print(str(elt_sym) + (3 - len(str(elt_sym))) * " " + 3 * " ", end="")
                print(str(elt_name) + (20 - len(str(elt_name))) * " ", end="")
                print(str(elt_mass) + (16 - len(str(elt_mass))) * " " + "\n", end="")
            print(55 * "=")

        # 2.) Seearch by atomic mass
        elif u_num == 2:
            min_mass = float(input("Please enter minimum mass: "))  # Get minimum mass from user
            max_mass = float(input("Please enter maximum mass: "))  # Get maximum mass from user
            
            # Find elements with atomic mass within the specified range
            found_elements_list = sorted([key for key, value in periodic_table.items()
                                          if min_mass <= value[2] <= max_mass])
            
            dict_1 = {i: periodic_table[i] for i in found_elements_list}  # Create a dictionary of found elements

            # Print header
            print()
            print(5 * " " + "#" + 3 * " " + "Sym" + 3 * " " + "Name" + 16 * " " + "Mass")
            print(55 * "=")

            # Print each found element's details
            for key, value in dict_1.items():
                elt_num1 = str(value[1])
                elt_sym1 = str(key)
                elt_name1 = str(value[0])
                elt_mass1 = str(value[2])

                print((6 - len(str(elt_num1))) * " " + str(elt_num1) + 3 * " ", end="")
                print(str(elt_sym1) + (3 - len(str(elt_sym1))) * " " + 3 * " ", end="")
                print(str(elt_name1) + (20 - len(str(elt_name1))) * " ", end="")
                print(str(elt_mass1) + (16 - len(str(elt_mass1))) * " " + "\n", end="")
            print(55 * "=")

        # 3.) Molecular Mass Calculation
        elif u_num == 3:
            molecular_formula = []  # Initialize an empty list to store the molecular formula

            while True:
                symb_str = str(input('Enter atomic symbol of element (. to stop): '))  # Get atomic symbol from user
                if symb_str == '.':
                    break  # Stop input if user enters '.'
                if symb_str in periodic_table:
                    atoms = int(input(f"Enter number of atoms of {symb_str} in molecule: "))  # Get number of atoms from user
                    molecular_formula.append((symb_str, atoms))  # Add the symbol and atom count to the formula
                else:
                    print("Unknown element. Please try again.")
            
            molecular_mass = 0  # Initialize molecular mass
            for symb_str, atoms in molecular_formula:
                if symb_str in periodic_table:
                    molecular_mass += atoms * periodic_table[symb_str][2]  # Calculate the molecular mass
                else:
                    print(f"Error: {symb_str} is not in the periodic table.")
                    return
            
            print(f'The molecular mass is: {molecular_mass}')  # Print the calculated molecular mass

        # 4.) Quit the program
        elif u_num == 4:
            print("Exiting the program.")
            break  # Exit the loop to quit the program

        else:
            print("Invalid choice. Please enter a valid number.")  # Handle invalid menu choice

run_program()
