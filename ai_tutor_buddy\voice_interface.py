import pyttsx3
import speech_recognition as sr
import time

class VoiceInterface:
    def __init__(self, rate=180, volume=1.0):
        # Initialize text-to-speech engine
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', rate)
        self.engine.setProperty('volume', volume)
        
        # Get available voices and set a default one
        voices = self.engine.getProperty('voices')
        self.engine.setProperty('voice', voices[0].id)  # Default voice
        
        # Initialize speech recognition
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Adjust for ambient noise
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source)
            
    def speak(self, text):
        """Convert text to speech"""
        print(f"🤖 AI: {text}")
        self.engine.say(text)
        self.engine.runAndWait()
        
    def listen(self, timeout=5, phrase_time_limit=None):
        """Listen for user input and convert to text"""
        print("🎤 Listening...")
        
        try:
            with self.microphone as source:
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
                
            text = self.recognizer.recognize_google(audio)
            print(f"👤 You: {text}")
            return text
        except sr.WaitTimeoutError:
            print("No speech detected within timeout period")
            return None
        except sr.UnknownValueError:
            print("Could not understand audio")
            return None
        except sr.RequestError as e:
            print(f"Could not request results; {e}")
            return None
            
    def change_voice(self, voice_index=0):
        """Change the voice of the TTS engine"""
        voices = self.engine.getProperty('voices')
        if 0 <= voice_index < len(voices):
            self.engine.setProperty('voice', voices[voice_index].id)
            return True
        return False