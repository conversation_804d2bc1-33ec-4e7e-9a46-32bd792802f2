# My Secret ID is: I1d9iRLNPkalR6fWUkCmvhFNa4dLrTy2


import requests
import time
import os
import json
from datetime import datetime, timedelta
from twilio.rest import Client

# API keys and credentials
SKYSCANNER_API_KEY = "I1d9iRLNPkalR6fWUkCmvhFNa4dLrTy2"  # Replace with your API key
TWILIO_ACCOUNT_SID = "**********************************"  # Replace with your Twilio SID
TWILIO_AUTH_TOKEN = "0a9df94a6c2885a7de136e9f62e3e598"    # Replace with your Twilio token
TWILIO_PHONE_NUMBER = ""       # Replace with your Twilio phone
YOUR_PHONE_NUMBER = "**********"         # Replace with your phone number

# Flight search parameters
ORIGIN_CITY = "NYC"  # Replace with your departure city code
DESTINATION = "SJU"  # San Juan, Puerto Rico
CURRENCY = "USD"

# Price threshold (alert when price falls below this)
PRICE_THRESHOLD = 300  # Set your desired price threshold

# File to store historical price data
PRICE_HISTORY_FILE = "price_history.json"

# Date range to search - 3 months ahead
def get_date_range():
    start_date = datetime.now() + timedelta(days=1)
    end_date = datetime.now() + timedelta(days=90)  # 3 months ahead
    return start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")

def load_price_history():
    if os.path.exists(PRICE_HISTORY_FILE):
        try:
            with open(PRICE_HISTORY_FILE, 'r') as f:
                return json.load(f)
        except:
            return []
    return []

def save_price_history(history):
    # Keep only the last 7 days of data
    history = history[-7:]
    with open(PRICE_HISTORY_FILE, 'w') as f:
        json.dump(history, f)

def search_flights():
    start_date, end_date = get_date_range()
    
    url = f"https://skyscanner-skyscanner-flight-search-v1.p.rapidapi.com/apiservices/browsequotes/v1.0/US/{CURRENCY}/en-US/{ORIGIN_CITY}/{DESTINATION}/{start_date}/{end_date}"
    
    headers = {
        "X-RapidAPI-Key": SKYSCANNER_API_KEY,
        "X-RapidAPI-Host": "skyscanner-skyscanner-flight-search-v1.p.rapidapi.com"
    }
    
    try:
        response = requests.get(url, headers=headers)
        data = response.json()
        
        if "Quotes" in data and data["Quotes"]:
            # Find the lowest price
            lowest_price = min(quote["MinPrice"] for quote in data["Quotes"])
            
            # Get details of the lowest price flight
            lowest_quote = next(quote for quote in data["Quotes"] if quote["MinPrice"] == lowest_price)
            departure_date = lowest_quote["OutboundLeg"]["DepartureDate"].split("T")[0]
            
            return {
                "price": lowest_price,
                "departure_date": departure_date,
                "direct": lowest_quote["Direct"],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        else:
            print("No flights found in the specified date range.")
            return None
    except Exception as e:
        print(f"Error searching flights: {e}")
        return None

def send_alert(flight_info, price_drop=None):
    client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
    
    message_body = (
        f"PRICE ALERT! Flights to Puerto Rico at ${flight_info['price']}!\n"
        f"Departure date: {flight_info['departure_date']}\n"
        f"Direct flight: {'Yes' if flight_info['direct'] else 'No'}"
    )
    
    if price_drop:
        message_body = (
            f"PRICE DROP ALERT! Flights to Puerto Rico dropped by ${price_drop}!\n"
            f"Current price: ${flight_info['price']}\n"
            f"Departure date: {flight_info['departure_date']}\n"
            f"Direct flight: {'Yes' if flight_info['direct'] else 'No'}"
        )
    
    try:
        message = client.messages.create(
            body=message_body,
            from_=TWILIO_PHONE_NUMBER,
            to=YOUR_PHONE_NUMBER
        )
        print(f"Alert sent! Message SID: {message.sid}")
    except Exception as e:
        print(f"Error sending alert: {e}")

def main():
    print("Flight Price Alert Service Started")
    print(f"Monitoring flights from {ORIGIN_CITY} to {DESTINATION}")
    print(f"Price threshold: ${PRICE_THRESHOLD}")
    print(f"Date range: Next 3 months")
    
    price_history = load_price_history()
    
    while True:
        print("\nChecking flight prices...")
        flight_info = search_flights()
        
        if flight_info:
            current_price = flight_info["price"]
            print(f"Current lowest price: ${current_price} on {flight_info['departure_date']}")
            
            # Add current price to history
            price_history.append({
                "price": current_price,
                "date": flight_info["departure_date"],
                "timestamp": flight_info["timestamp"]
            })
            save_price_history(price_history)
            
            # Check if price is below threshold
            if current_price < PRICE_THRESHOLD:
                send_alert(flight_info)
            
            # Check if price dropped compared to past week
            if len(price_history) > 1:
                # Get average price from past week (excluding today)
                past_prices = [item["price"] for item in price_history[:-1]]
                if past_prices:
                    avg_past_price = sum(past_prices) / len(past_prices)
                    
                    # If price dropped by at least 10% from average
                    if current_price < avg_past_price * 0.9:
                        price_drop = round(avg_past_price - current_price, 2)
                        send_alert(flight_info, price_drop)
                        print(f"Price dropped by ${price_drop} from average of ${avg_past_price}")
        
        # Wait before checking again (6 hours)
        print("Waiting 6 hours before next check...")
        time.sleep(6 * 60 * 60)

if __name__ == "__main__":
    main()