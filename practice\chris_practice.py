def is_prime(integer):  # checking to see if prime
    """def is_prime(integer: int) -> bool:, to make sure type is correct
    'typehinting'
    #
    Returns a boolean: True if value is a prime number, False otherwise.
    """

    if integer <= 1:
        return False
    for factor in range(2, int(integer ** 0.5) + 1): # only checks up to square root for efficiancy
        if integer % factor == 0:
            return False
    return True


def reverse(value):  # reverse of value, ex. 35 -> 53
    """Assumes value is a positive integer. Returns an integer that is
    the reverse of value (e.g. if value is 735, returns 537)."""
    value = str(value)  # converting to string
    counter = len(value) - 1  # getting index, if len = 10, max index = 9
    rev_val = ""
    for _ in value:  # _ to repace usless "char / loop variable", not using it!
        rev_val = rev_val + value[counter]  # adding to counter
        counter = counter - 1  # subtracting from counter (becasue reversing)
    return int(rev_val)


# Input validation
while True:
    try:
        val = int(input("Please enter a positive number: "))
        if val > 0:
            break
        else:
            print("Please enter a positive integer greater than zero.")
    except ValueError:
        print("Invalid input. Please enter a positive integer.")

test = 2  # c intializes int_string as 2 initially
num_primes = 1  # where you are in string, starting at 1,\
#  if zero would break at 1st line


while num_primes - 1 < val:  # if forward and backwards is prime, if wasn't -1\
    #  wouldnt work with above
    if is_prime(test) and is_prime(reverse(test)):
        if num_primes % 5 == 0:  # new line every 4 outputs
            print(test)
            test += 1  # test is output, started at 2
            num_primes += 1  # number of times output will be printed
        else:
            spaces = 10 - len(str(test))  # setting number of spaces in\
            #  between outputs
            print(test, end=" " * spaces)  # changing spaces according to\
            #  output
            test += 1  # test is output, started at 2
            num_primes += 1  # number of times output will be printed
    else:
        test += 1  # if number not prime still have to increase test\
        #  (keep going)