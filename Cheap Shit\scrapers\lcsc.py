import requests
from bs4 import BeautifulSoup

def search_lcsc(part_name):
    url = f"https://www.lcsc.com/search?q={part_name}"
    headers = {"User-Agent": "Mozilla/5.0"}
    r = requests.get(url, headers=headers)
    soup = BeautifulSoup(r.text, "html.parser")

    results = []
    for item in soup.select(".product-list-item")[:10]:  # Limit results
        try:
            name = item.select_one(".product-title").text.strip()
            price = item.select_one(".product-price").text.strip()
            link = "https://www.lcsc.com" + item.select_one("a")["href"]
            results.append({"name": name, "price": price, "link": link, "source": "LCSC"})
        except Exception:
            continue

    return results
