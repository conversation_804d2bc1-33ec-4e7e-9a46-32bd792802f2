# Electronics Practice Simulator

A web-based application for practicing electronics with virtual components and circuit simulation. Perfect for learning basic electronics concepts, building circuits, and understanding how electronic components work together.

## Features

### 🔧 Virtual Components
- **Resistors** (220Ω, 470Ω, 1kΩ, etc.)
- **LEDs** (Red, Green, Blue, Yellow, White)
- **Batteries** (1.5V, 3V, 5V, 9V, 12V)
- **Capacitors** (Various values)
- **Switches** (Open/Closed states)
- **Wires** for connections

### ⚡ Circuit Simulation
- Real-time circuit analysis using Oh<PERSON>'s law
- Voltage, current, and power calculations
- LED brightness simulation
- Visual feedback for component states

### 📚 Interactive Tutorials
- Step-by-step guided lessons
- Basic LED circuit tutorial
- Voltage divider circuits
- Progressive difficulty levels

### 🎨 User Interface
- Drag-and-drop component placement
- Visual breadboard workspace
- Connection points for wiring
- Real-time simulation results
- Component measurement display

## Installation & Setup

### Backend Setup

1. **Navigate to the backend directory:**
   ```bash
   cd electronics_simulator/backend
   ```

2. **Create a virtual environment (recommended):**
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the Flask backend:**
   ```bash
   python app.py
   ```
   
   The backend will start on `http://localhost:5000`

### Frontend Setup

1. **Navigate to the frontend directory:**
   ```bash
   cd electronics_simulator/frontend
   ```

2. **Open the application:**
   - Simply open `index.html` in your web browser
   - Or use a local server:
     ```bash
     # Using Python
     python -m http.server 8000
     
     # Using Node.js (if you have it)
     npx serve .
     ```

3. **Access the application:**
   - Direct file: Open `index.html` in your browser
   - Local server: Visit `http://localhost:8000`

## How to Use

### Building Your First Circuit

1. **Add Components:**
   - Drag components from the sidebar to the workspace
   - Components include batteries, resistors, LEDs, etc.

2. **Make Connections:**
   - Click on connection points (red dots) on components
   - Click another connection point to create a wire
   - Right-click components or wires to delete them

3. **Simulate:**
   - Click the "▶️ Simulate" button to run the circuit analysis
   - View results in the results panel
   - Watch LEDs light up based on current flow

### Tutorial Mode

1. **Access Tutorials:**
   - Click "📚 Tutorials" in the toolbar
   - Choose from available tutorials
   - Follow step-by-step instructions

2. **Learn Progressively:**
   - Start with basic LED circuits
   - Progress to more complex circuits
   - Understand voltage dividers and current flow

### Example Circuits

#### Basic LED Circuit
1. Add a 9V battery to the workspace
2. Add a 470Ω resistor
3. Add a red LED
4. Connect: Battery(+) → Resistor → LED(+) → LED(-) → Battery(-)
5. Click simulate to see the LED light up!

#### Voltage Divider
1. Add a 9V battery
2. Add two resistors (1kΩ each)
3. Connect them in series with the battery
4. Simulate to see voltage distribution

## Technical Details

### Backend (Flask)
- **Circuit Analysis:** Implements basic Ohm's law calculations
- **Component Library:** Manages electronic component properties
- **Simulation Engine:** Calculates voltage, current, and power
- **API Endpoints:** RESTful API for frontend communication

### Frontend (HTML/CSS/JavaScript)
- **Drag & Drop:** Intuitive component placement
- **Canvas Rendering:** Visual circuit representation
- **Real-time Updates:** Live simulation feedback
- **Responsive Design:** Works on desktop and tablet

### Simulation Accuracy
- Uses fundamental electrical laws (Ohm's law, Kirchhoff's laws)
- Simplified model suitable for educational purposes
- Real-time calculations for immediate feedback
- Component-specific behaviors (LED forward voltage, etc.)

## Educational Value

### Learning Objectives
- Understand basic electrical concepts
- Learn component symbols and functions
- Practice circuit building skills
- Visualize current flow and voltage drops
- Develop troubleshooting abilities

### Suitable For
- Electronics beginners
- Students learning basic circuits
- Hobbyists exploring electronics
- Teachers demonstrating concepts
- Anyone curious about how electronics work

## Future Enhancements

### Planned Features
- More component types (transistors, op-amps, etc.)
- Advanced simulation (AC analysis, frequency response)
- Circuit saving and loading
- Collaborative circuit sharing
- Mobile app version
- Arduino simulation integration

### Contributing
This is an educational project. Contributions welcome for:
- Additional component types
- More sophisticated simulation algorithms
- Enhanced user interface features
- Educational content and tutorials
- Bug fixes and optimizations

## Troubleshooting

### Common Issues

1. **Backend not starting:**
   - Check Python installation
   - Verify all dependencies are installed
   - Ensure port 5000 is available

2. **Frontend not connecting:**
   - Verify backend is running on localhost:5000
   - Check browser console for CORS errors
   - Ensure both frontend and backend are running

3. **Simulation not working:**
   - Check that components are properly connected
   - Ensure at least one voltage source is present
   - Verify wire connections are complete

### Browser Compatibility
- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

This project is created for educational purposes. Feel free to use, modify, and distribute for learning and teaching electronics.

## Support

For questions or issues:
1. Check the troubleshooting section
2. Review the tutorial content
3. Experiment with different circuit configurations
4. Remember: learning electronics is hands-on - keep building and testing!
