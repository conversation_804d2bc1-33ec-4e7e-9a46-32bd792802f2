import json
import os
import time
from datetime import datetime
import chromadb
from config import MEMORY_FILE, VECTOR_DB_PATH, DEFAULT_USER

class MemorySystem:
    def __init__(self):
        self.user_data = self._load_user_data()
        self.conversation_history = []
        
        # Initialize vector database for semantic memory
        self.chroma_client = chromadb.PersistentClient(path=VECTOR_DB_PATH)
        
        # Create or get collections
        try:
            self.memory_collection = self.chroma_client.get_collection("semantic_memories")
        except:
            self.memory_collection = self.chroma_client.create_collection("semantic_memories")
    
    def _load_user_data(self):
        """Load user data from file or create default"""
        os.makedirs(os.path.dirname(MEMORY_FILE), exist_ok=True)
        
        if os.path.exists(MEMORY_FILE):
            try:
                with open(MEMORY_FILE, 'r') as f:
                    return json.load(f)
            except:
                return DEFAULT_USER.copy()
        else:
            # Create default user data
            with open(MEMORY_FILE, 'w') as f:
                json.dump(DEFAULT_USER, f, indent=2)
            return DEFAULT_USER.copy()
    
    def save_user_data(self):
        """Save user data to file"""
        with open(MEMORY_FILE, 'w') as f:
            json.dump(self.user_data, f, indent=2)
    
    def add_conversation(self, user_input, ai_response):
        """Add a conversation exchange to history"""
        timestamp = datetime.now().isoformat()
        self.conversation_history.append({
            "timestamp": timestamp,
            "user": user_input,
            "ai": ai_response
        })
        
        # Also add to vector database for semantic search
        self.memory_collection.add(
            documents=[f"User: {user_input}\nAI: {ai_response}"],
            metadatas=[{"timestamp": timestamp, "type": "conversation"}],
            ids=[f"conv_{int(time.time())}_{len(self.conversation_history)}"]
        )
    
    def add_subject(self, subject):
        """Add a subject the user is learning"""
        if subject not in self.user_data["subjects"]:
            self.user_data["subjects"].append(subject)
            self.save_user_data()
    
    def add_goal(self, goal):
        """Add a learning goal"""
        if goal not in self.user_data["goals"]:
            self.user_data["goals"].append(goal)
            self.save_user_data()
    
    def update_name(self, name):
        """Update user's name"""
        self.user_data["name"] = name
        self.save_user_data()
    
    def update_learning_style(self, style):
        """Update user's preferred learning style"""
        self.user_data["learning_style"] = style
        self.save_user_data()
    
    def get_recent_conversations(self, limit=5):
        """Get recent conversations"""
        return self.conversation_history[-limit:] if self.conversation_history else []
    
    def search_memories(self, query, limit=3):
        """Search for relevant memories based on query"""
        if not query:
            return []
            
        results = self.memory_collection.query(
            query_texts=[query],
            n_results=limit
        )
        
        if results and len(results["documents"]) > 0:
            return results["documents"][0]
        return []