import random
import time

class Battle:
    """Class handling Pokemon battles"""
    def __init__(self, player, wild_pokemon, ui):
        self.player = player
        self.wild_pokemon = wild_pokemon
        self.ui = ui
        self.active_pokemon = player.pokemon[0]  # First Pokemon in party
        self.turn = 0
    
    def start(self):
        """Start a battle with a wild Pokemon"""
        # <PERSON> as seen in Pokedex
        self.player.mark_pokemon_seen(self.wild_pokemon.name)
        
        # Battle loop
        while True:
            self.turn += 1
            
            # Display battle status
            self.ui.clear_screen()
            self.ui.print_battle_status(self.active_pokemon, self.wild_pokemon)
            
            # Player's turn
            player_action = self.player_turn()
            
            # Check if battle ended during player's turn
            if player_action == "ran":
                return "ran"
            elif player_action == "caught":
                return "caught"
            elif self.wild_pokemon.hp <= 0:
                # Wild Pokemon fainted
                self.ui.print_message(f"Wild {self.wild_pokemon.name} fainted!")
                
                # Award XP
                xp_gained = self.calculate_xp(self.wild_pokemon)
                self.ui.print_message(f"{self.active_pokemon.name} gained {xp_gained} XP!")
                
                # Check for level up
                level_up_result = self.active_pokemon.gain_xp(xp_gained)
                if level_up_result:
                    self.ui.print_message(f"{self.active_pokemon.name} grew to level {self.active_pokemon.level}!")
                    
                    # Check for evolution
                    if isinstance(level_up_result, dict) and level_up_result.get("type") == "move_learned":
                        self.ui.print_message(f"{self.active_pokemon.name} learned {level_up_result['move']['name']}!")
                    elif isinstance(level_up_result, dict) and level_up_result.get("type") == "move_full":
                        self.ui.print_message(f"{self.active_pokemon.name} wants to learn {level_up_result['move']['name']}!")
                        self.ui.print_message("But it already knows 4 moves!")
                        # TODO: Implement move forgetting
                
                self.ui.wait_for_input()
                return "won"
            
            # Wild Pokemon's turn
            if self.wild_pokemon.hp > 0:
                self.wild_pokemon_turn()
                
                if self.active_pokemon.hp <= 0:
                    # Player's active Pokemon fainted
                    self.ui.print_message(f"{self.active_pokemon.name} fainted!")
                    
                    # Check if player has other Pokemon
                    alive_pokemon = [p for p in self.player.pokemon if p.hp > 0]
                    
                    if not alive_pokemon:
                        # All Pokemon fainted, player lost
                        self.ui.print_message("You have no usable Pokemon!")
                        self.ui.wait_for_input()
                        return "lost"
                    else:
                        # Switch to another Pokemon
                        options = [f"{p.name} (Lv. {p.level}) - HP: {p.hp}/{p.max_hp}" for p in alive_pokemon]
                        self.ui.print_message("Choose your next Pokemon:")
                        choice = self.ui.menu("Select a Pokemon:", options)
                        self.active_pokemon = alive_pokemon[choice]
                        self.ui.print_message(f"Go, {self.active_pokemon.name}!")
                        self.ui.wait_for_input()
    
    def player_turn(self):
        """Handle the player's turn in battle"""
        options = ["Fight", "Bag", "Pokemon", "Run"]
        choice = self.ui.menu("What will you do?", options)
        
        if choice == 0:  # Fight
            return self.handle_fight()
        elif choice == 1:  # Bag
            return self.handle_bag()
        elif choice == 2:  # Pokemon
            return self.handle_pokemon_switch()
        elif choice == 3:  # Run
            # Chance to run based on speed and number of attempts
            run_chance = (self.active_pokemon.speed * 128) / (self.wild_pokemon.speed + 1) + 30 * self.turn
            run_chance = min(255, run_chance) / 255
            
            if random.random() < run_chance:
                self.ui.print_message("Got away safely!")
                self.ui.wait_for_input()
                return "ran"
            else:
                self.ui.print_message("Can't escape!")
                self.ui.wait_for_input()
                return None
    
    def handle_fight(self):
        """Handle the fight option in battle"""
        # Show moves
        options = [f"{m['name']} ({m['type']} type)" for m in self.active_pokemon.moves]
        options.append("Back")
        
       