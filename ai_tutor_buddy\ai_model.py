from openai import OpenAI
from config import OPENAI_API_KEY

class AIModel:
    def __init__(self):
        self.client = OpenAI(api_key=OPENAI_API_KEY)
        self.model = "gpt-4o-mini"  # Using a more affordable model, change as needed
    
    def generate_response(self, prompt, user_data, conversation_history, relevant_memories=None):
        """Generate a response from the AI model"""
        if not OPENAI_API_KEY:
            return "Error: OpenAI API key not found. Please set it in your environment variables."
        
        # Create system message with personality and user info
        system_message = self._create_system_message(user_data)
        
        # Format conversation history
        messages = [{"role": "system", "content": system_message}]
        
        # Add relevant memories if available
        if relevant_memories and len(relevant_memories) > 0:
            memory_text = "Here are some relevant memories that might help with your response:\n\n"
            memory_text += "\n\n".join(relevant_memories)
            messages.append({"role": "system", "content": memory_text})
        
        # Add recent conversation history (last 5 exchanges)
        for exchange in conversation_history[-5:]:
            messages.append({"role": "user", "content": exchange["user"]})
            messages.append({"role": "assistant", "content": exchange["ai"]})
        
        # Add the current prompt
        messages.append({"role": "user", "content": prompt})
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=500
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"Sorry, I encountered an error: {str(e)}"
    
    def _create_system_message(self, user_data):
        """Create a system message with the AI's personality and user info"""
        return f"""You are a friendly, personal AI tutor for {user_data['name']}. 
        
Your personality is warm, encouraging, and slightly humorous. You explain concepts clearly and relate them to real-world examples.

The student's learning goals are: {', '.join(user_data['goals'])}
The subjects they're interested in are: {', '.join(user_data['subjects'])}
Their preferred learning style is: {user_data['learning_style']}

When explaining concepts:
1. Start with a simple explanation
2. Provide examples
3. Relate to things they already know
4. Ask questions to check understanding

Always maintain a conversational tone as if you're speaking, not writing an essay."""