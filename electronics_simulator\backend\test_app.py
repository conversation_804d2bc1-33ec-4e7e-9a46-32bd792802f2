import unittest
import json
import sys
import os

# Add the backend directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, Circuit, Component

class TestElectronicsSimulator(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.app = app.test_client()
        self.app.testing = True
        self.circuit = Circuit()
    
    def test_component_library_endpoint(self):
        """Test the component library API endpoint."""
        response = self.app.get('/api/components')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIn('resistor', data)
        self.assertIn('led', data)
        self.assertIn('battery', data)
        self.assertIn('capacitor', data)
        
        # Check resistor properties
        resistor = data['resistor']
        self.assertEqual(resistor['symbol'], 'R')
        self.assertEqual(resistor['unit'], 'Ω')
        self.assertIsInstance(resistor['values'], list)
        self.assertGreater(len(resistor['values']), 0)
    
    def test_circuit_update_endpoint(self):
        """Test updating circuit configuration."""
        circuit_data = {
            'components': [
                {
                    'id': 'bat1',
                    'type': 'battery',
                    'value': 9,
                    'position': {'x': 100, 'y': 100}
                },
                {
                    'id': 'res1',
                    'type': 'resistor',
                    'value': 470,
                    'position': {'x': 200, 'y': 100}
                }
            ],
            'wires': [
                {
                    'from': {'componentId': 'bat1', 'pointIndex': 0},
                    'to': {'componentId': 'res1', 'pointIndex': 0}
                }
            ]
        }
        
        response = self.app.post('/api/circuit',
                               data=json.dumps(circuit_data),
                               content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')
    
    def test_basic_simulation(self):
        """Test basic circuit simulation."""
        # Add components to circuit
        battery = Component('bat1', 'battery', 9, {'x': 100, 'y': 100})
        resistor = Component('res1', 'resistor', 470, {'x': 200, 'y': 100})
        
        self.circuit.components['bat1'] = battery
        self.circuit.components['res1'] = resistor
        
        # Run simulation
        results = self.circuit.simulate()
        
        # Check results
        self.assertIsInstance(results, dict)
        if 'res1' in results:
            resistor_data = results['res1']
            self.assertIn('voltage', resistor_data)
            self.assertIn('current', resistor_data)
            self.assertIn('power', resistor_data)
            
            # Basic Ohm's law check
            voltage = resistor_data['voltage']
            current = resistor_data['current']
            expected_voltage = current * 470
            self.assertAlmostEqual(voltage, expected_voltage, places=2)
    
    def test_led_simulation(self):
        """Test LED circuit simulation."""
        # Create LED circuit
        battery = Component('bat1', 'battery', 9, {'x': 100, 'y': 100})
        resistor = Component('res1', 'resistor', 470, {'x': 200, 'y': 100})
        led = Component('led1', 'led', 0, {'x': 300, 'y': 100})
        led.color = 'red'
        
        self.circuit.components['bat1'] = battery
        self.circuit.components['res1'] = resistor
        self.circuit.components['led1'] = led
        
        results = self.circuit.simulate()
        
        if 'led1' in results:
            led_data = results['led1']
            self.assertIn('brightness', led_data)
            self.assertIn('on', led_data)
            self.assertIsInstance(led_data['on'], bool)
            self.assertGreaterEqual(led_data['brightness'], 0)
            self.assertLessEqual(led_data['brightness'], 1)
    
    def test_ac_analysis(self):
        """Test AC analysis functionality."""
        # Add capacitor for AC analysis
        capacitor = Component('cap1', 'capacitor', 100, {'x': 100, 'y': 100})
        self.circuit.components['cap1'] = capacitor
        
        results = self.circuit.ac_analysis(1000)  # 1kHz
        
        if 'cap1' in results:
            cap_data = results['cap1']
            self.assertIn('impedance_magnitude', cap_data)
            self.assertIn('impedance_phase', cap_data)
            self.assertIn('reactance', cap_data)
            self.assertEqual(cap_data['frequency'], 1000)
    
    def test_power_analysis(self):
        """Test power analysis functionality."""
        # Create simple circuit
        battery = Component('bat1', 'battery', 9, {'x': 100, 'y': 100})
        resistor = Component('res1', 'resistor', 100, {'x': 200, 'y': 100})
        
        self.circuit.components['bat1'] = battery
        self.circuit.components['res1'] = resistor
        
        results = self.circuit.power_analysis()
        
        self.assertIn('circuit_summary', results)
        summary = results['circuit_summary']
        self.assertIn('total_power_supplied', summary)
        self.assertIn('total_power_consumed', summary)
        self.assertIn('efficiency_percent', summary)
    
    def test_simulation_endpoint(self):
        """Test the simulation API endpoint."""
        # First update circuit
        circuit_data = {
            'components': [
                {
                    'id': 'bat1',
                    'type': 'battery',
                    'value': 9,
                    'position': {'x': 100, 'y': 100}
                }
            ],
            'wires': []
        }
        
        self.app.post('/api/circuit',
                     data=json.dumps(circuit_data),
                     content_type='application/json')
        
        # Run simulation
        response = self.app.post('/api/simulate')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, dict)
    
    def test_ac_simulation_endpoint(self):
        """Test AC simulation API endpoint."""
        # Update circuit with capacitor
        circuit_data = {
            'components': [
                {
                    'id': 'cap1',
                    'type': 'capacitor',
                    'value': 100,
                    'position': {'x': 100, 'y': 100}
                }
            ],
            'wires': []
        }
        
        self.app.post('/api/circuit',
                     data=json.dumps(circuit_data),
                     content_type='application/json')
        
        # Test AC analysis
        ac_data = {'frequency': 1000}
        response = self.app.post('/api/simulate/ac',
                               data=json.dumps(ac_data),
                               content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIsInstance(data, dict)
    
    def test_tutorials_endpoint(self):
        """Test tutorials API endpoint."""
        response = self.app.get('/api/tutorials')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)
        
        # Check tutorial structure
        tutorial = data[0]
        self.assertIn('id', tutorial)
        self.assertIn('title', tutorial)
        self.assertIn('description', tutorial)
        self.assertIn('difficulty', tutorial)
    
    def test_specific_tutorial_endpoint(self):
        """Test specific tutorial retrieval."""
        response = self.app.get('/api/tutorial/basic_led')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['id'], 'basic_led')
        self.assertIn('circuit', data)
    
    def test_invalid_tutorial_endpoint(self):
        """Test invalid tutorial ID."""
        response = self.app.get('/api/tutorial/nonexistent')
        self.assertEqual(response.status_code, 404)
        
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def test_component_creation(self):
        """Test Component class creation."""
        component = Component('test1', 'resistor', 1000, {'x': 50, 'y': 50})
        
        self.assertEqual(component.id, 'test1')
        self.assertEqual(component.type, 'resistor')
        self.assertEqual(component.value, 1000)
        self.assertEqual(component.position['x'], 50)
        self.assertEqual(component.position['y'], 50)
        self.assertEqual(component.connections, [])
    
    def test_circuit_component_management(self):
        """Test adding and managing components in circuit."""
        component_data = {
            'id': 'test_comp',
            'type': 'resistor',
            'value': 220,
            'position': {'x': 100, 'y': 200}
        }
        
        component = self.circuit.add_component(component_data)
        
        self.assertEqual(component.id, 'test_comp')
        self.assertIn('test_comp', self.circuit.components)
        self.assertEqual(self.circuit.components['test_comp'], component)
    
    def test_error_handling_no_voltage_source(self):
        """Test simulation error when no voltage source is present."""
        # Add only a resistor, no battery
        resistor = Component('res1', 'resistor', 1000, {'x': 100, 'y': 100})
        self.circuit.components['res1'] = resistor
        
        results = self.circuit.simulate()
        self.assertIn('error', results)
        self.assertEqual(results['error'], 'No voltage source found')

if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
