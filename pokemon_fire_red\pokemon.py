import random
import json

class Pokemon:
    """Class representing a Pokemon"""
    def __init__(self, name, type, level=5, moves=None, hp=None, max_hp=None, 
                 attack=None, defense=None, sp_attack=None, sp_defense=None, speed=None,
                 xp=0, evolution=None):
        self.name = name
        self.type = type
        self.level = level
        self.moves = moves or []
        
        # Base stats calculation
        self.max_hp = max_hp or (level * 3 + 20)
        self.hp = hp if hp is not None else self.max_hp
        self.attack = attack or (level * 2 + 5)
        self.defense = defense or (level * 2 + 5)
        self.sp_attack = sp_attack or (level * 2 + 5)
        self.sp_defense = sp_defense or (level * 2 + 5)
        self.speed = speed or (level * 2 + 5)
        
        # Experience points
        self.xp = xp
        self.xp_to_level = level * 100
        
        # Evolution data
        self.evolution = evolution
    
    def level_up(self):
        """Level up the Pokemon and increase its stats"""
        self.level += 1
        self.xp = 0
        self.xp_to_level = self.level * 100
        
        # Increase stats
        old_max_hp = self.max_hp
        self.max_hp = int(self.level * 3 + 20)
        self.hp += (self.max_hp - old_max_hp)
        self.attack = int(self.level * 2 + 5)
        self.defense = int(self.level * 2 + 5)
        self.sp_attack = int(self.level * 2 + 5)
        self.sp_defense = int(self.level * 2 + 5)
        self.speed = int(self.level * 2 + 5)
        
        # Check for evolution
        if self.evolution and self.level >= self.evolution["level"]:
            return self.evolution["evolves_to"]
        
        # Check for new moves
        new_move = self.check_new_moves()
        return new_move
    
    def check_new_moves(self):
        """Check if the Pokemon learns a new move at this level"""
        # This would normally be loaded from a data file
        # Simplified version for demonstration
        move_learn_levels = {
            "Charmander": {
                7: {"name": "Ember", "power": 40, "type": "Fire", "category": "Special"},
                13: {"name": "Metal Claw", "power": 50, "type": "Steel", "category": "Physical"},
                19: {"name": "Flame Burst", "power": 70, "type": "Fire", "category": "Special"},
                25: {"name": "Fire Fang", "power": 65, "type": "Fire", "category": "Physical"}
            },
            "Squirtle": {
                7: {"name": "Water Gun", "power": 40, "type": "Water", "category": "Special"},
                13: {"name": "Bite", "power": 60, "type": "Dark", "category": "Physical"},
                19: {"name": "Rapid Spin", "power": 50, "type": "Normal", "category": "Physical"},
                25: {"name": "Aqua Tail", "power": 90, "type": "Water", "category": "Physical"}
            },
            "Bulbasaur": {
                7: {"name": "Vine Whip", "power": 45, "type": "Grass", "category": "Physical"},
                13: {"name": "Poison Powder", "power": 0, "type": "Poison", "category": "Status"},
                19: {"name": "Razor Leaf", "power": 55, "type": "Grass", "category": "Physical"},
                25: {"name": "Sleep Powder", "power": 0, "type": "Grass", "category": "Status"}
            }
        }
        
        if self.name in move_learn_levels and self.level in move_learn_levels[self.name]:
            new_move = move_learn_levels[self.name][self.level]
            
            # Check if already has 4 moves
            if len(self.moves) >= 4:
                return {"type": "move_full", "move": new_move}
            else:
                self.moves.append(new_move)
                return {"type": "move_learned", "move": new_move}
        
        return None
    
    def gain_xp(self, amount):
        """Add experience points to the Pokemon"""
        self.xp += amount
        if self.xp >= self.xp_to_level:
            return self.level_up()
        return None
    
    def to_dict(self):
        """Convert Pokemon to dictionary for saving"""
        return {
            "name": self.name,
            "type": self.type,
            "level": self.level,
            "moves": self.moves,
            "hp": self.hp,
            "max_hp": self.max_hp,
            "attack": self.attack,
            "defense": self.defense,
            "sp_attack": self.sp_attack,
            "sp_defense": self.sp_defense,
            "speed": self.speed,
            "xp": self.xp,
            "evolution": self.evolution
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create Pokemon from dictionary data"""
        return cls(
            name=data["name"],
            type=data["type"],
            level=data["level"],
            moves=data["moves"],
            hp=data["hp"],
            max_hp=data["max_hp"],
            attack=data["attack"],
            defense=data["defense"],
            sp_attack=data.get("sp_attack"),
            sp_defense=data.get("sp_defense"),
            speed=data["speed"],
            xp=data["xp"],
            evolution=data.get("evolution")
        )


class PokemonFactory:
    """Factory class for creating Pokemon"""
    def __init__(self):
        # Load Pokemon data
        self.pokemon_data = self._load_pokemon_data()
    
    def _load_pokemon_data(self):
        """Load Pokemon data from a file or use default data"""
        # In a real game, this would load from a JSON file
        # For simplicity, we'll define it here
        return {
            "Charmander": {
                "type": "Fire",
                "base_moves": [
                    {"name": "Scratch", "power": 40, "type": "Normal", "category": "Physical"},
                    {"name": "Growl", "power": 0, "type": "Normal", "category": "Status"}
                ],
                "evolution": {"level": 16, "evolves_to": "Charmeleon"}
            },
            "Squirtle": {
                "type": "Water",
                "base_moves": [
                    {"name": "Tackle", "power": 40, "type": "Normal", "category": "Physical"},
                    {"name": "Tail Whip", "power": 0, "type": "Normal", "category": "Status"}
                ],
                "evolution": {"level": 16, "evolves_to": "Wartortle"}
            },
            "Bulbasaur": {
                "type": "Grass",
                "base_moves": [
                    {"name": "Tackle", "power": 40, "type": "Normal", "category": "Physical"},
                    {"name": "Growl", "power": 0, "type": "Normal", "category": "Status"}
                ],
                "evolution": {"level": 16, "evolves_to": "Ivysaur"}
            },
            "Pidgey": {
                "type": "Flying",
                "base_moves": [
                    {"name": "Tackle", "power": 40, "type": "Normal", "category": "Physical"},
                    {"name": "Sand Attack", "power": 0, "type": "Ground", "category": "Status"}
                ],
                "evolution": {"level": 18, "evolves_to": "Pidgeotto"}
            },
            "Rattata": {
                "type": "Normal",
                "base_moves": [
                    {"name": "Tackle", "power": 40, "type": "Normal", "category": "Physical"},
                    {"name": "Tail Whip", "power": 0, "type": "Normal", "category": "Status"}
                ],
                "evolution": {"level": 20, "evolves_to": "Raticate"}
            }
        }
    
    def create_starter(self, name):
        """Create a starter Pokemon"""
        if name not in self.pokemon_data:
            raise ValueError(f"Unknown Pokemon: {name}")
        
        data = self.pokemon_data[name]
        return Pokemon(
            name=name,
            type=data["type"],
            level=5,
            moves=data["base_moves"].copy(),
            evolution=data.get("evolution")
        )
    
    def create_wild_pokemon(self, name, level_range):
        """Create a wild Pokemon with a random level in the given range"""
        if name not in self.pokemon_data:
            raise ValueError(f"Unknown Pokemon: {name}")
        
        data = self.pokemon_data[name]
        level = random.randint(level_range[0], level_range[1])
        
        return Pokemon(
            name=name,
            type=data["type"],
            level=level,
            moves=data["base_moves"].copy(),
            evolution=data.get("evolution")
        )
    
    def create_from_dict(self, data):
        """Create a Pokemon from dictionary data"""
        return Pokemon.from_dict(data)