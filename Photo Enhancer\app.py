import gradio as gr
import cv2
import numpy as np
import torch
import os
import tempfile
from datetime import datetime
from PIL import Image
from basicsr.archs.rrdbnet_arch import RRDBNet
from realesrgan import RealESRGANer
from gfpgan import GFPGANer

# Create temp directory for processing
TEMP_DIR = os.path.join(tempfile.gettempdir(), 'neighborhood_watch_temp')
os.makedirs(TEMP_DIR, exist_ok=True)

# Initialize models
def initialize_models():
    # Initialize Real-ESRGAN
    model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
    bg_upsampler = RealESRGANer(
        scale=4,
        model_path='https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
        model=model,
        tile=400,
        tile_pad=10,
        pre_pad=0,
        half=True
    )
    
    # Initialize GFPGAN for face enhancement
    face_enhancer = GFPGANer(
        model_path='https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.3.pth',
        upscale=4,
        arch='clean',
        channel_multiplier=2,
        bg_upsampler=bg_upsampler
    )
    
    return bg_upsampler, face_enhancer

# Process image function
def enhance_image(input_image, enhance_faces=True, denoise_level=0, improve_lighting=False, location="", date="", time=""):
    if input_image is None:
        return None, "Please upload an image."
    
    # Convert to numpy array if needed
    if isinstance(input_image, Image.Image):
        input_image = np.array(input_image)
    
    # Initialize models (in real app, do this once at startup)
    bg_upsampler, face_enhancer = initialize_models()
    
    # Apply denoising if requested
    if denoise_level > 0:
        denoise_strength = denoise_level * 5  # Scale 0-10 to 0-50
        input_image = cv2.fastNlMeansDenoisingColored(input_image, None, denoise_strength, denoise_strength, 7, 21)
    
    # Improve lighting if requested
    if improve_lighting:
        lab = cv2.cvtColor(input_image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
        cl = clahe.apply(l)
        limg = cv2.merge((cl, a, b))
        input_image = cv2.cvtColor(limg, cv2.COLOR_LAB2BGR)
    
    # Process with GFPGAN (face enhancement) and Real-ESRGAN (background)
    if enhance_faces:
        _, _, output = face_enhancer.enhance(input_image, has_aligned=False, only_center_face=False, paste_back=True)
    else:
        output, _ = bg_upsampler.enhance(input_image, outscale=4)
    
    # Create side-by-side comparison
    h, w = input_image.shape[:2]
    output_resized = cv2.resize(output, (w*2, h*2))
    
    # Add metadata if provided
    metadata = []
    if location:
        metadata.append(f"Location: {location}")
    if date:
        metadata.append(f"Date: {date}")
    if time:
        metadata.append(f"Time: {time}")
    
    # Add timestamp
    metadata.append(f"Enhanced: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Add metadata to image
    if metadata:
        y_offset = 30
        for line in metadata:
            cv2.putText(output_resized, line, (10, y_offset), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y_offset += 30
    
    return output_resized, "Image enhanced successfully."

# Create Gradio interface
def create_interface():
    with gr.Blocks(title="Neighborhood Watch Image Enhancement Tool") as app:
        gr.Markdown("""
        # Neighborhood Watch Image Enhancement Tool
        
        This tool helps enhance low-quality security footage to better identify suspicious activities.
        
        ## Privacy & Ethics Notice
        - This tool does NOT use facial recognition or identity matching
        - No images or data are stored permanently
        - Do NOT use this tool to publicly accuse individuals without proper verification
        - Always follow local laws and regulations when using enhanced images
        """)
        
        with gr.Row():
            with gr.Column():
                input_image = gr.Image(label="Upload Image", type="numpy")
                with gr.Row():
                    enhance_faces = gr.Checkbox(label="Enhance Faces", value=True)
                    denoise_level = gr.Slider(minimum=0, maximum=10, value=3, label="Denoise Level")
                    improve_lighting = gr.Checkbox(label="Improve Lighting", value=True)
                
                with gr.Row():
                    location = gr.Textbox(label="Location (optional)")
                    date = gr.Textbox(label="Date (optional)")
                    time = gr.Textbox(label="Time (optional)")
                
                enhance_btn = gr.Button("Enhance Image")
            
            with gr.Column():
                output_image = gr.Image(label="Enhanced Image")
                status = gr.Textbox(label="Status")
        
        enhance_btn.click(
            fn=enhance_image,
            inputs=[input_image, enhance_faces, denoise_level, improve_lighting, location, date, time],
            outputs=[output_image, status]
        )
        
        gr.Markdown("""
        ## How to Use
        1. Upload a low-quality image from security footage
        2. Adjust enhancement settings as needed
        3. Click "Enhance Image"
        4. Download the enhanced result
        
        ## Legal Disclaimer
        This tool is provided for legitimate security and safety purposes only. Users are responsible for ensuring compliance with all applicable laws regarding privacy, surveillance, and data protection.
        """)
    
    return app

# Launch the app
if __name__ == "__main__":
    app = create_interface()
    app.launch()