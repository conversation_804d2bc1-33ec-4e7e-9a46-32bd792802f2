import os
import time
import shutil
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class DownloadSorter(FileSystemEventHandler):
    def __init__(self, download_dir):
        self.download_dir = download_dir
        self.pictures_dir = os.path.join(download_dir, "Pictures")
        self.pdfs_dir = os.path.join(download_dir, "PDFs")
        
        # Create destination folders if they don't exist
        for directory in [self.pictures_dir, self.pdfs_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
    
    def on_created(self, event):
        if event.is_directory:
            return
        
        # Get file path and extension
        file_path = event.src_path
        _, extension = os.path.splitext(file_path)
        extension = extension.lower()
        
        # Move file to appropriate folder
        if extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            self.move_file(file_path, self.pictures_dir)
        elif extension == '.pdf':
            self.move_file(file_path, self.pdfs_dir)
    
    def move_file(self, source, destination_dir):
        try:
            filename = os.path.basename(source)
            destination = os.path.join(destination_dir, filename)
            
            # Wait a moment to ensure file is fully downloaded
            time.sleep(1)
            shutil.move(source, destination)
            print(f"Moved {filename} to {destination_dir}")
        except Exception as e:
            print(f"Error moving {source}: {e}")

def main():
    # Set your downloads directory here
    downloads_dir = os.path.expanduser("~/Downloads")
    
    event_handler = DownloadSorter(downloads_dir)
    observer = Observer()
    observer.schedule(event_handler, downloads_dir, recursive=False)
    observer.start()
    
    print(f"Monitoring downloads in {downloads_dir}...")
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()

if __name__ == "__main__":
    main()