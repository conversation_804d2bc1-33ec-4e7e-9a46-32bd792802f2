import random
import time
import json
import os
import sys
from pokemon import Pokemon, PokemonFactory
from player import Player
from battle import Battle
from world import World
from ui import TextUI

class PokemonGame:
    def __init__(self):
        self.ui = TextUI()
        self.world = None
        self.player = None
        self.pokemon_factory = PokemonFactory()
        self.save_dir = "saves"
        
        # Create save directory if it doesn't exist
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
    
    def start(self):
        """Main entry point for the game"""
        self.ui.clear_screen()
        self.ui.print_title("POKEMON FIRE RED")
        self.ui.print_centered("Python Edition")
        self.ui.print_centered("Created by: Your Name")
        self.ui.print_line()
        
        # Main menu
        options = ["New Game", "Load Game", "Options", "Exit"]
        choice = self.ui.menu("Main Menu", options)
        
        if choice == 0:  # New Game
            self.new_game()
        elif choice == 1:  # Load Game
            self.load_game()
        elif choice == 2:  # Options
            self.show_options()
            self.start()  # Return to main menu after options
        elif choice == 3:  # Exit
            self.ui.print_message("Thanks for playing!")
            sys.exit()
    
    def new_game(self):
        """Start a new game"""
        self.ui.clear_screen()
        self.ui.print_title("NEW GAME")
        
        # Introduction
        self.ui.print_dialog("Professor Oak", "Hello there! Welcome to the world of POKEMON!")
        self.ui.print_dialog("Professor Oak", "My name is OAK! People call me the POKEMON PROF!")
        self.ui.print_dialog("Professor Oak", "This world is inhabited by creatures called POKEMON!")
        self.ui.print_dialog("Professor Oak", "For some people, POKEMON are pets. Others use them for fights.")
        self.ui.print_dialog("Professor Oak", "Myself... I study POKEMON as a profession.")
        
        # Get player name
        player_name = self.ui.input_text("First, what is your name?")
        
        # Create player
        self.player = Player(name=player_name)
        
        # Choose starter
        self.ui.print_dialog("Professor Oak", f"Right! So your name is {player_name}!")
        self.ui.print_dialog("Professor Oak", "Your very own POKEMON legend is about to unfold!")
        self.ui.print_dialog("Professor Oak", "A world of dreams and adventures with POKEMON awaits! Let's go!")
        self.ui.print_dialog("Professor Oak", "Here, I have three POKEMON. Choose one as your starter!")
        
        starters = [
            {"name": "Charmander", "type": "Fire", "description": "The flame on its tail shows its life force."},
            {"name": "Squirtle", "type": "Water", "description": "A turtle POKEMON that shoots water."},
            {"name": "Bulbasaur", "type": "Grass", "description": "A strange seed was planted on its back at birth."}
        ]
        
        options = [f"{s['name']} ({s['type']} type): {s['description']}" for s in starters]
        choice = self.ui.menu("Choose your starter Pokemon:", options)
        
        # Create starter Pokemon
        starter_data = starters[choice]
        starter = self.pokemon_factory.create_starter(starter_data["name"])
        self.player.add_pokemon(starter)
        
        self.ui.print_message(f"You chose {starter.name}!")
        self.ui.print_dialog("Professor Oak", f"Excellent choice! {starter.name} is a great Pokemon!")
        self.ui.print_dialog("Professor Oak", "Now, your journey begins! Good luck, young trainer!")
        
        # Initialize world
        self.world = World(self.player, self.pokemon_factory)
        
        # Start game loop
        self.game_loop()
    
    def load_game(self):
        """Load a saved game"""
        self.ui.clear_screen()
        self.ui.print_title("LOAD GAME")
        
        # Get save files
        save_files = [f for f in os.listdir(self.save_dir) if f.endswith(".json")]
        
        if not save_files:
            self.ui.print_message("No save files found!")
            self.ui.wait_for_input()
            self.start()
            return
        
        # Show save files
        options = [f.replace(".json", "") for f in save_files]
        options.append("Back to Main Menu")
        
        choice = self.ui.menu("Select a save file:", options)
        
        if choice == len(options) - 1:  # Back option
            self.start()
            return
        
        # Load selected save
        save_file = os.path.join(self.save_dir, save_files[choice])
        try:
            with open(save_file, "r") as f:
                save_data = json.load(f)
            
            # Recreate player
            self.player = Player.from_dict(save_data["player"], self.pokemon_factory)
            
            # Recreate world
            self.world = World.from_dict(save_data["world"], self.player, self.pokemon_factory)
            
            self.ui.print_message(f"Game loaded successfully! Welcome back, {self.player.name}!")
            self.ui.wait_for_input()
            
            # Start game loop
            self.game_loop()
            
        except Exception as e:
            self.ui.print_message(f"Error loading save file: {e}")
            self.ui.wait_for_input()
            self.load_game()
    
    def save_game(self):
        """Save the current game"""
        self.ui.clear_screen()
        self.ui.print_title("SAVE GAME")
        
        # Create save data
        save_data = {
            "player": self.player.to_dict(),
            "world": self.world.to_dict()
        }
        
        # Save to file
        save_file = os.path.join(self.save_dir, f"{self.player.name}.json")
        try:
            with open(save_file, "w") as f:
                json.dump(save_data, f, indent=2)
            
            self.ui.print_message("Game saved successfully!")
        except Exception as e:
            self.ui.print_message(f"Error saving game: {e}")
        
        self.ui.wait_for_input()
    
    def show_options(self):
        """Show game options"""
        self.ui.clear_screen()
        self.ui.print_title("OPTIONS")
        
        options = ["Text Speed", "Battle Animation", "Back"]
        choice = self.ui.menu("Options:", options)
        
        if choice == 0:  # Text Speed
            speeds = ["Slow", "Normal", "Fast"]
            speed_choice = self.ui.menu("Text Speed:", speeds)
            self.ui.set_text_speed(speed_choice)
            self.ui.print_message(f"Text speed set to {speeds[speed_choice]}!")
            self.ui.wait_for_input()
            self.show_options()
        
        elif choice == 1:  # Battle Animation
            options = ["On", "Off"]
            anim_choice = self.ui.menu("Battle Animation:", options)
            self.ui.set_battle_animation(anim_choice == 0)
            self.ui.print_message(f"Battle animation turned {options[anim_choice]}!")
            self.ui.wait_for_input()
            self.show_options()
    
    def game_loop(self):
        """Main game loop"""
        while True:
            # Display current location
            self.ui.clear_screen()
            current_location = self.world.get_current_location()
            self.ui.print_title(current_location.name)
            self.ui.print_description(current_location.description)
            
            # Show available actions
            options = ["Move", "Pokemon", "Bag", "Save", "Quit"]
            choice = self.ui.menu("What would you like to do?", options)
            
            if choice == 0:  # Move
                self.handle_movement()
            elif choice == 1:  # Pokemon
                self.show_pokemon()
            elif choice == 2:  # Bag
                self.show_bag()
            elif choice == 3:  # Save
                self.save_game()
            elif choice == 4:  # Quit
                confirm = self.ui.confirm("Are you sure you want to quit? Unsaved progress will be lost.")
                if confirm:
                    self.start()
                    return
    
    def handle_movement(self):
        """Handle player movement between locations"""
        current_location = self.world.get_current_location()
        connections = current_location.connections
        
        if not connections:
            self.ui.print_message("There's nowhere to go from here!")
            self.ui.wait_for_input()
            return
        
        # Show available connections
        options = [self.world.get_location_by_id(conn).name for conn in connections]
        options.append("Back")
        
        choice = self.ui.menu("Where would you like to go?", options)
        
        if choice == len(options) - 1:  # Back option
            return
        
        # Move to selected location
        new_location_id = connections[choice]
        
        # Check if this is a route (chance for wild encounter)
        new_location = self.world.get_location_by_id(new_location_id)
        
        self.ui.print_message(f"Traveling to {new_location.name}...")
        self.ui.wait_for_input(1)  # Short delay
        
        # Move player
        self.world.move_player(new_location_id)
        
        # Check for wild encounter
        if new_location.is_route and random.random() < 0.3:
            self.wild_encounter()
    
    def wild_encounter(self):
        """Handle a wild Pokemon encounter"""
        current_location = self.world.get_current_location()
        
        # Get a random wild Pokemon for this location
        wild_pokemon = self.world.get_wild_pokemon(current_location.id)
        
        if not wild_pokemon:
            return  # No wild Pokemon available
        
        self.ui.print_wild_encounter(wild_pokemon)
        
        # Start battle
        battle = Battle(self.player, wild_pokemon, self.ui)
        result = battle.start()
        
        if result == "caught":
            self.ui.print_message(f"You caught {wild_pokemon.name}!")
            self.player.add_pokemon(wild_pokemon)
        elif result == "ran":
            self.ui.print_message("You got away safely!")
        elif result == "won":
            self.ui.print_message(f"You defeated {wild_pokemon.name}!")
            # Give player some money
            money_earned = wild_pokemon.level * 5
            self.player.money += money_earned
            self.ui.print_message(f"You earned ${money_earned}!")
        elif result == "lost":
            self.ui.print_message("You were defeated!")
            self.ui.print_message("You rush to the nearest Pokemon Center...")
            self.player.heal_all_pokemon()
            # Move player to last town with Pokemon Center
            self.world.move_player_to_last_center()
        
        self.ui.wait_for_input()
    
    def show_pokemon(self):
        """Show player's Pokemon"""
        while True:
            self.ui.clear_screen()
            self.ui.print_title("YOUR POKEMON")
            
            if not self.player.pokemon:
                self.ui.print_message("You don't have any Pokemon!")
                self.ui.wait_for_input()
                return
            
            # Show Pokemon list
            options = [f"{p.name} (Lv. {p.level}) - HP: {p.hp}/{p.max_hp}" for p in self.player.pokemon]
            options.append("Back")
            
            choice = self.ui.menu("Select a Pokemon:", options)
            
            if choice == len(options) - 1:  # Back option
                return
            
            # Show Pokemon details
            selected = self.player.pokemon[choice]
            self.show_pokemon_details(selected)
    
    def show_pokemon_details(self, pokemon):
        """Show details for a specific Pokemon"""
        while True:
            self.ui.clear_screen()
            self.ui.print_title(f"{pokemon.name} (Lv. {pokemon.level})")
            self.ui.print_pokemon_stats(pokemon)
            
            # Show options
            options = ["Summary", "Switch Position", "Use Item", "Back"]
            choice = self.ui.menu("What would you like to do?", options)
            
            if choice == 0:  # Summary
                self.ui.show_pokemon_summary(pokemon)
            elif choice == 1:  # Switch Position
                self.switch_pokemon_position(pokemon)
            elif choice == 2:  # Use Item
                self.use_item_on_pokemon(pokemon)
            elif choice == 3:  # Back
                return
    
    def switch_pokemon_position(self, pokemon):
        """Switch position of Pokemon in party"""
        current_idx = self.player.pokemon.index(pokemon)
        
        # Show other Pokemon
        options = [f"{p.name} (Lv. {p.level})" for p in self.player.pokemon]
        options.append("Cancel")
        
        self.ui.print_message(f"Switch {pokemon.name} with which Pokemon?")
        choice = self.ui.menu("Select a Pokemon:", options)
        
        if choice == len(options) - 1:  # Cancel option
            return
        
        # Switch positions
        self.player.switch_pokemon(current_idx, choice)
        self.ui.print_message(f"Switched {pokemon.name} with {self.player.pokemon[choice].name}!")
        self.ui.wait_for_input()
    
    def use_item_on_pokemon(self, pokemon):
        """Use an item on a Pokemon"""
        # Show usable items
        usable_items = [item for item, count in self.player.items.items() 
                        if count > 0 and item in ["Potion", "Super Potion", "Hyper Potion", "Rare Candy"]]
        
        if not usable_items:
            self.ui.print_message("You don't have any usable items!")
            self.ui.wait_for_input()
            return
        
        options = [f"{item} (x{self.player.items[item]})" for item in usable_items]
        options.append("Cancel")
        
        choice = self.ui.menu("Use which item?", options)
        
        if choice == len(options) - 1:  # Cancel option
            return
        
        selected_item = usable_items[choice]
        
        # Use the item
        if selected_item == "Potion":
            if pokemon.hp == pokemon.max_hp:
                self.ui.print_message(f"{pokemon.name} is already at full health!")
            else:
                heal_amount = min(20, pokemon.max_hp - pokemon.hp)
                pokemon.hp += heal_amount
                self.player.items["Potion"] -= 1
                self.ui.print_message(f"Used a Potion! {pokemon.name} recovered {heal_amount} HP!")
        
        elif selected_item == "Super Potion":
            if pokemon.hp == pokemon.max_hp:
                self.ui.print_message(f"{pokemon.name} is already at full health!")
            else:
                heal_amount = min(50, pokemon.max_hp - pokemon.hp)
                pokemon.hp += heal_amount
                self.player.items["Super Potion"] -= 1
                self.ui.print_message(f"Used a Super Potion! {pokemon.name} recovered {heal_amount} HP!")
        
        elif selected_item == "Hyper Potion":
            if pokemon.hp == pokemon.max_hp:
                self.ui.print_message(f"{pokemon.name} is already at full health!")
            else:
                heal_amount = min(200, pokemon.max_hp - pokemon.hp)
                pokemon.hp += heal_amount
                self.player.items["Hyper Potion"] -= 1
                self.ui.print_message(f"Used a Hyper Potion! {pokemon.name} recovered {heal_amount} HP!")
        
        elif selected_item == "Rare Candy":
            old_level = pokemon.level
            pokemon.level_up()
            self.player.items["Rare Candy"] -= 1
            self.ui.print_message(f"Used a Rare Candy! {pokemon.name} grew from level {old_level} to {pokemon.level}!")
        
        self.ui.wait_for_input()
    
    def show_bag(self):
        """Show player's bag/inventory"""
        self.ui.clear_screen()
        self.ui.print_title("BAG")
        
        if not self.player.items:
            self.ui.print_message("Your bag is empty!")
            self.ui.wait_for_input()
            return
        
        # Group items by category
        categories = {
            "Medicine": ["Potion", "Super Potion", "Hyper Potion", "Revive"],
            "Pokeballs": ["Pokeball", "Great Ball", "Ultra Ball"],
            "Key Items": ["Town Map", "Bicycle", "Fishing Rod"],
            "TMs & HMs": ["TM01", "TM02", "HM01", "HM02"]
        }
        
        # Show categories
        category_names = list(categories.keys())
        category_names.append("Back")
        
        choice = self.ui.menu("Select a category:", category_names)
        
        if choice == len(category_names) - 1:  # Back option
            return
        
        # Show items in selected category
        selected_category = category_names[choice]
        category_items = categories[selected_category]
        
        # Filter items the player actually has
        available_items = [item for item in category_items if item in self.player.items and self.player.items[item] > 0]
        
        if not available_items:
            self.ui.print_message(f"You don't have any {selected_category} items!")
            self.ui.wait_for_input()
            return
        
        # Show available items
        options = [f"{item} (x{self.player.items[item]})" for item in available_items]
        options.append("Back")
        
        item_choice = self.ui.menu(f"Select an item:", options)
        
        if item_choice == len(options) - 1:  # Back option
            self.show_bag()
            return
        
        selected_item = available_items[item_choice]
        
        # Show item options
        item_options = ["Use", "Toss", "Back"]
        action_choice = self.ui.menu(f"What would you like to do with {selected_item}?", item_options)
        
        if action_choice == 0:  # Use
            self.use_item(selected_item)
        elif action_choice == 1:  # Toss
            self.toss_item(selected_item)
        else:  # Back
            self.show_bag()
    
    def use_item(self, item):
        """Use a selected item"""
        # Different behavior based on item type
        if item in ["Potion", "Super Potion", "Hyper Potion", "Revive"]:
            # Select Pokemon to use item on
            if not self.player.pokemon:
                self.ui.print_message("You don't have any Pokemon!")
                self.ui.wait_for_input()
                return
            
            options = [f"{p.name} (Lv. {p.level}) - HP: {p.hp}/{p.max_hp}" for p in self.player.pokemon]
            options.append("Cancel")
            
            choice = self.ui.menu(f"Use {item} on which Pokemon?", options)
            
            if choice == len(options) - 1:  # Cancel option
                return
            
            selected_pokemon = self.player.pokemon[choice]
            
            # Use the item