import random
import json

class Location:
    """Class representing a location in the game world"""
    def __init__(self, id, name, description, connections=None, is_route=False, 
                 wild_pokemon=None, trainers=None, has_pokemon_center=False):
        self.id = id
        self.name = name
        self.description = description
        self.connections = connections or []
        self.is_route = is_route
        self.wild_pokemon = wild_pokemon or {}  # Dict of Pokemon name -> level range
        self.trainers = trainers or []
        self.has_pokemon_center = has_pokemon_center
    
    def to_dict(self):
        """Convert location to dictionary for saving"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "connections": self.connections,
            "is_route": self.is_route,
            "wild_pokemon": self.wild_pokemon,
            "trainers": self.trainers,
            "has_pokemon_center": self.has_pokemon_center
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create location from dictionary data"""
        return cls(
            id=data["id"],
            name=data["name"],
            description=data["description"],
            connections=data["connections"],
            is_route=data["is_route"],
            wild_pokemon=data["wild_pokemon"],
            trainers=data["trainers"],
            has_pokemon_center=data["has_pokemon_center"]
        )


class World:
    """Class representing the game world"""
    def __init__(self, player, pokemon_factory):
        self.player = player
        self.pokemon_factory = pokemon_factory
        self.locations = self._create_world()
        self.current_location_id = player.position["map"]
    
    def _create_world(self):
        """Create the game world with locations"""
        # In a real game, this would load from a data file
        # For simplicity, we'll define a small world here
        locations = {}
        
        # Pallet Town
        locations["pallet_town"] = Location(
            id="pallet_town",
            name="Pallet Town",
            description="A small, peaceful town where your journey begins. Home to Professor Oak's lab.",
            connections=["route_1"],
            has_pokemon_center=True
        )
        
        # Route 1
        locations["route_1"] = Location(
            id="route_1",
            name="Route 1",
            description="A grassy path connecting Pallet Town and Viridian City.",
            connections=["pallet_town", "viridian_city"],
            is_route=True,
            wild_pokemon={
                "Pidgey": [2, 5],
                "Rattata": [2, 4]
            }
        )
        
        # Viridian City
        locations["viridian_city"] = Location(
            id="viridian_city",
            name="Viridian City",
            description="The closest city to Pallet Town. Home to the first Pokemon Gym, but the leader is often away.",
            connections=["route_1", "route_2", "route_22"],
            has_pokemon_center=True
        )
        
        # Route 2
        locations["route_2"] = Location(
            id="route_2",
            name="Route 2",
            description="A route connecting Viridian City to Viridian Forest.",
            connections=["viridian_city", "viridian_forest"],
            is_route=True,
            wild_pokemon={
                "Pidgey": [3, 6],
                "Rattata": [3, 5],
                "Weedle": [3, 5]
            }
        )
        
        # Route 22
        locations["route_22"] = Location(
            id="route_22",
            name="Route 22",
            description="A route leading west from Viridian City toward the Pokemon League.",
            connections=["viridian_city"],
            is_route=True,
            wild_pokemon={
                "Rattata": [2, 5],
                "Spearow": [3, 6]
            }
        )
        
        # Viridian Forest
        locations["viridian_forest"] = Location(
            id="viridian_forest",
            name="Viridian Forest",
            description="A dense forest full of Bug Pokemon and Bug Catchers.",
            connections=["route_2", "pewter_city"],
            is_route=True,
            wild_pokemon={
                "Caterpie": [3, 6],
                "Weedle": [3, 6],
                "Metapod": [4, 7],
                "Kakuna": [4, 7],
                "Pikachu": [5, 8]
            }
        )
        
        # Pewter City
        locations["pewter_city"] = Location(
            id="pewter_city",
            name="Pewter City",
            description="A city nestled between rugged mountains and rocks. Home to the Rock-type Gym.",
            connections=["viridian_forest", "route_3"],
            has_pokemon_center=True
        )
        
        return locations
    
    def get_current_location(self):
        """Get the current location object"""
        return self.locations[self.current_location_id]
    
    def get_location_by_id(self, location_id):
        """Get a location by its ID"""
        return self.locations.get(location_id)
    
    def move_player(self, location_id):
        """Move the player to a new location"""
        if location_id in self.locations:
            self.current_location_id = location_id
            self.player.position["map"] = location_id
            
            # Update last Pokemon Center if applicable
            if self.locations[location_id].has_pokemon_center:
                self.player.last_pokemon_center = location_id
            
            return True
        return False
    
    def move_player_to_last_center(self):
        """Move player to the last visited Pokemon Center"""
        return self.move_player(self.player.last_pokemon_center)
    
    def get_wild_pokemon(self, location_id):
        """Get a random wild Pokemon for the given location"""
        location = self.locations.get(location_id)
        if not location or not location.wild_pokemon:
            return None
        
        # Choose a random Pokemon species from the location
        pokemon_name = random.choice(list(location.wild_pokemon.keys()))
        level_range = location.wild_pokemon[pokemon_name]
        
        # Create and return the Pokemon
        return self.pokemon_factory.create_wild_pokemon(pokemon_name, level_range)
    
    def to_dict(self):
        """Convert world data to dictionary for saving"""
        return {
            "current_location_id": self.current_location_id,
            "locations": {loc_id: loc.to_dict() for loc_id, loc in self.locations.items()}
        }
    
    @classmethod
    def from_dict(cls, data, player, pokemon_factory):
        """Create world from saved data"""
        world = cls(player, pokemon_factory)
        
        # Load locations
        world.locations = {
            loc_id: Location.from_dict(loc_data) 
            for loc_id, loc_data in data["locations"].items()
        }
        
        # Set current location
        world.current_location_id = data["current_location_id"]
        
        return world