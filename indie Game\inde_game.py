import pygame
import sys
import random
import string

# Initialize pygame
pygame.init()

# Game constants
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)

class Letter:
    def __init__(self, char, lane):
        self.char = char
        self.lane = lane
        self.x = 100 + lane * 100
        self.y = 0
        self.speed = 3
        self.hit = False
        self.missed = False
        
    def update(self):
        self.y += self.speed
        if self.y > SCREEN_HEIGHT - 50 and not self.hit:
            self.missed = True
            
    def draw(self, screen):
        color = GREEN if self.hit else (RED if self.missed else WHITE)
        font = pygame.font.Font(None, 36)
        text = font.render(self.char, True, color)
        screen.blit(text, (self.x - text.get_width() // 2, self.y))

class TypingHero:
    def __init__(self):
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Typing Hero")
        self.clock = pygame.time.Clock()
        self.running = True
        self.letters = []
        self.score = 0
        self.misses = 0
        self.spawn_timer = 0
        self.spawn_delay = 60  # Frames between letter spawns
        self.lanes = 6  # Number of lanes
        
    def spawn_letter(self):
        lane = random.randint(0, self.lanes - 1)
        char = random.choice(string.ascii_lowercase)
        self.letters.append(Letter(char, lane))
        
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                else:
                    # Convert key press to character
                    try:
                        pressed_char = chr(event.key).lower()
                        self.check_hit(pressed_char)
                    except ValueError:
                        pass
                        
    def check_hit(self, char):
        # Find the lowest letter matching the pressed key
        target = None
        target_idx = -1
        
        for i, letter in enumerate(self.letters):
            if letter.char == char and not letter.hit and not letter.missed:
                if target is None or letter.y > target.y:
                    target = letter
                    target_idx = i
        
        if target and SCREEN_HEIGHT - 150 <= target.y <= SCREEN_HEIGHT - 50:
            # Perfect hit zone
            target.hit = True
            self.score += 10
        elif target:
            # Hit but not in perfect zone
            target.hit = True
            self.score += 5
                
    def update(self):
        # Spawn new letters
        self.spawn_timer += 1
        if self.spawn_timer >= self.spawn_delay:
            self.spawn_letter()
            self.spawn_timer = 0
            
        # Update letters
        for letter in self.letters:
            letter.update()
            if letter.missed:
                self.misses += 1
                
        # Remove hit or missed letters
        self.letters = [l for l in self.letters if not (l.hit or l.missed)]
        
    def render(self):
        self.screen.fill(BLACK)
        
        # Draw target zone
        pygame.draw.rect(self.screen, BLUE, (0, SCREEN_HEIGHT - 150, SCREEN_WIDTH, 100), 2)
        
        # Draw letters
        for letter in self.letters:
            letter.draw(self.screen)
            
        # Draw score
        font = pygame.font.Font(None, 36)
        score_text = font.render(f"Score: {self.score}", True, WHITE)
        self.screen.blit(score_text, (10, 10))
        
        # Draw misses
        misses_text = font.render(f"Misses: {self.misses}", True, WHITE)
        self.screen.blit(misses_text, (10, 50))
        
        pygame.display.flip()
        
    def run(self):
        while self.running:
            self.handle_events()
            self.update()
            self.render()
            self.clock.tick(FPS)
        pygame.quit()
        sys.exit()

# Run the game
if __name__ == "__main__":
    game = TypingHero()
    game.run()