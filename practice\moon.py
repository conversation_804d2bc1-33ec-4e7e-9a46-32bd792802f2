
def moon_phase(phase):
  emoji = ['🌑', '🌒', '🌓', '🌔', '🌕', '🌖', '🌗', '🌘']
  if phase == 'New Moon':
    print(emoji[0])
  elif phase == 'Waxing Crescent':
    print(emoji[1])
  elif phase == 'First Quarter':
    print(emoji[2])
  elif phase == 'Waxing Gibbous':
    print(emoji[3])
  elif phase == 'Full Moon':
    print(emoji[4])
  elif phase == 'Waning Gibbous':
    print(emoji[5])
  elif phase == 'Last Quarter':
    print(emoji[6])
  elif phase == 'Waning Crescent':
    print(emoji[7])
  else:
    print('Invalid moon phase')
  
  
moon_phase('Waxing Crescent')