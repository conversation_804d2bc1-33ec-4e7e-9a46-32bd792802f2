{"basic_led_circuit": {"name": "Basic LED Circuit", "description": "Simple LED circuit with current-limiting resistor", "difficulty": "beginner", "learning_objectives": ["Understand current limiting with resistors", "Learn LED polarity", "Calculate appropriate resistor values"], "components": [{"id": "bat1", "type": "battery", "value": 9, "position": {"x": 100, "y": 150}, "description": "9V battery provides power"}, {"id": "res1", "type": "resistor", "value": 470, "position": {"x": 250, "y": 150}, "description": "470Ω resistor limits current to safe level"}, {"id": "led1", "type": "led", "color": "red", "position": {"x": 400, "y": 150}, "description": "Red LED - note the polarity!"}], "wires": [{"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res1", "pointIndex": 0}, "description": "Connect battery positive to resistor"}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "led1", "pointIndex": 0}, "description": "Connect resistor to LED anode"}, {"from": {"componentId": "led1", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}, "description": "Connect LED cathode to battery negative"}], "expected_results": {"led_current": "19.1 mA", "led_voltage": "1.8 V", "resistor_voltage": "7.2 V", "total_power": "172 mW"}}, "voltage_divider": {"name": "Voltage Divider Circuit", "description": "Two resistors in series creating voltage division", "difficulty": "beginner", "learning_objectives": ["Understand voltage division principle", "Calculate output voltage", "Learn about series resistance"], "components": [{"id": "bat1", "type": "battery", "value": 12, "position": {"x": 100, "y": 100}}, {"id": "res1", "type": "resistor", "value": 1000, "position": {"x": 250, "y": 100}, "description": "Upper resistor - 1kΩ"}, {"id": "res2", "type": "resistor", "value": 2200, "position": {"x": 250, "y": 200}, "description": "Lower resistor - 2.2kΩ"}], "wires": [{"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res1", "pointIndex": 0}}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "res2", "pointIndex": 0}}, {"from": {"componentId": "res2", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}], "expected_results": {"output_voltage": "8.25 V", "current": "3.75 mA", "voltage_ratio": "0.6875"}}, "parallel_resistors": {"name": "Parallel Resistor Circuit", "description": "Two resistors connected in parallel", "difficulty": "intermediate", "learning_objectives": ["Understand parallel resistance calculation", "Learn current division", "Compare with series circuits"], "components": [{"id": "bat1", "type": "battery", "value": 9, "position": {"x": 100, "y": 150}}, {"id": "res1", "type": "resistor", "value": 1000, "position": {"x": 300, "y": 100}, "description": "First parallel resistor - 1kΩ"}, {"id": "res2", "type": "resistor", "value": 2200, "position": {"x": 300, "y": 200}, "description": "Second parallel resistor - 2.2kΩ"}], "wires": [{"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res1", "pointIndex": 0}}, {"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res2", "pointIndex": 0}}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}, {"from": {"componentId": "res2", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}], "expected_results": {"total_resistance": "687.5 Ω", "total_current": "13.1 mA", "res1_current": "9 mA", "res2_current": "4.1 mA"}}, "rc_circuit": {"name": "RC Charging Circuit", "description": "Resistor-Capacitor circuit demonstrating charging behavior", "difficulty": "intermediate", "learning_objectives": ["Understand capacitor charging", "Learn time constants", "Observe exponential behavior"], "components": [{"id": "bat1", "type": "battery", "value": 5, "position": {"x": 100, "y": 150}}, {"id": "res1", "type": "resistor", "value": 10000, "position": {"x": 250, "y": 150}, "description": "10kΩ charging resistor"}, {"id": "cap1", "type": "capacitor", "value": 100, "position": {"x": 400, "y": 150}, "description": "100μF capacitor"}, {"id": "sw1", "type": "switch", "position": {"x": 175, "y": 150}, "description": "Switch to start charging"}], "wires": [{"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "sw1", "pointIndex": 0}}, {"from": {"componentId": "sw1", "pointIndex": 1}, "to": {"componentId": "res1", "pointIndex": 0}}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "cap1", "pointIndex": 0}}, {"from": {"componentId": "cap1", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}], "expected_results": {"time_constant": "1.0 seconds", "final_voltage": "5 V", "63_percent_time": "1.0 seconds"}}, "multi_led_circuit": {"name": "Multiple LED Circuit", "description": "Circuit with multiple LEDs of different colors", "difficulty": "intermediate", "learning_objectives": ["Understand different LED forward voltages", "Calculate individual resistor values", "Learn about LED characteristics"], "components": [{"id": "bat1", "type": "battery", "value": 12, "position": {"x": 100, "y": 200}}, {"id": "res1", "type": "resistor", "value": 510, "position": {"x": 250, "y": 100}, "description": "Resistor for red LED"}, {"id": "led1", "type": "led", "color": "red", "position": {"x": 350, "y": 100}}, {"id": "res2", "type": "resistor", "value": 470, "position": {"x": 250, "y": 200}, "description": "Resistor for green LED"}, {"id": "led2", "type": "led", "color": "green", "position": {"x": 350, "y": 200}}, {"id": "res3", "type": "resistor", "value": 390, "position": {"x": 250, "y": 300}, "description": "Resistor for blue LED"}, {"id": "led3", "type": "led", "color": "blue", "position": {"x": 350, "y": 300}}], "wires": [{"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res1", "pointIndex": 0}}, {"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res2", "pointIndex": 0}}, {"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res3", "pointIndex": 0}}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "led1", "pointIndex": 0}}, {"from": {"componentId": "res2", "pointIndex": 1}, "to": {"componentId": "led2", "pointIndex": 0}}, {"from": {"componentId": "res3", "pointIndex": 1}, "to": {"componentId": "led3", "pointIndex": 0}}, {"from": {"componentId": "led1", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}, {"from": {"componentId": "led2", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}, {"from": {"componentId": "led3", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}}], "expected_results": {"red_led_current": "20 mA", "green_led_current": "21 mA", "blue_led_current": "22.6 mA", "total_current": "63.6 mA"}}, "oscilloscope_demo": {"name": "Oscilloscope Demonstration", "description": "Function generator connected to oscilloscope probe for waveform viewing", "difficulty": "intermediate", "learning_objectives": ["Learn to use function generators", "Understand oscilloscope probe connections", "Observe AC waveforms in real-time", "Practice signal measurement techniques"], "components": [{"id": "fg1", "type": "function_generator", "frequency": 1000, "amplitude": 5, "waveform": "sine", "position": {"x": 100, "y": 150}, "description": "1kHz sine wave generator"}, {"id": "probe1", "type": "oscilloscope_probe", "attenuation": 1, "position": {"x": 300, "y": 150}, "description": "1x oscilloscope probe"}, {"id": "res1", "type": "resistor", "value": 1000, "position": {"x": 200, "y": 150}, "description": "1kΩ load resistor"}], "wires": [{"from": {"componentId": "fg1", "pointIndex": 0}, "to": {"componentId": "res1", "pointIndex": 0}, "description": "Function generator output to resistor"}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "probe1", "pointIndex": 0}, "description": "Resistor to oscilloscope probe"}, {"from": {"componentId": "probe1", "pointIndex": 1}, "to": {"componentId": "fg1", "pointIndex": 1}, "description": "Ground connection"}], "expected_results": {"waveform": "1kHz sine wave", "amplitude": "5V peak-to-peak", "frequency": "1000 Hz"}}, "signal_analysis_circuit": {"name": "Multi-Signal Analysis", "description": "Multiple signal sources with different probes for comparison", "difficulty": "advanced", "learning_objectives": ["Compare different signal types", "Use multiple oscilloscope channels", "Understand signal characteristics", "Practice advanced measurement techniques"], "components": [{"id": "fg1", "type": "function_generator", "frequency": 1000, "amplitude": 5, "waveform": "sine", "position": {"x": 100, "y": 100}}, {"id": "pg1", "type": "pulse_generator", "frequency": 1000, "amplitude": 5, "position": {"x": 100, "y": 200}}, {"id": "sg1", "type": "signal_generator", "frequency": 2000, "amplitude": 3, "position": {"x": 100, "y": 300}}, {"id": "probe1", "type": "oscilloscope_probe", "position": {"x": 300, "y": 100}}, {"id": "probe2", "type": "oscilloscope_probe", "position": {"x": 300, "y": 200}}, {"id": "probe3", "type": "oscilloscope_probe", "position": {"x": 300, "y": 300}}], "wires": [{"from": {"componentId": "fg1", "pointIndex": 0}, "to": {"componentId": "probe1", "pointIndex": 0}}, {"from": {"componentId": "pg1", "pointIndex": 0}, "to": {"componentId": "probe2", "pointIndex": 0}}, {"from": {"componentId": "sg1", "pointIndex": 0}, "to": {"componentId": "probe3", "pointIndex": 0}}], "expected_results": {"channel_1": "1kHz sine wave, 5V amplitude", "channel_2": "1kHz pulse wave, 5V amplitude", "channel_3": "2kHz sine wave, 3V amplitude"}}, "electron_flow_demo": {"name": "Electron Flow Visualization", "description": "Simple circuit to demonstrate animated electron flow", "difficulty": "beginner", "learning_objectives": ["Visualize current flow direction", "Understand electron movement", "See the relationship between current and electron speed", "Learn basic circuit analysis"], "components": [{"id": "bat1", "type": "battery", "value": 12, "position": {"x": 100, "y": 200}}, {"id": "res1", "type": "resistor", "value": 100, "position": {"x": 300, "y": 150}}, {"id": "res2", "type": "resistor", "value": 200, "position": {"x": 500, "y": 150}}, {"id": "led1", "type": "led", "color": "red", "position": {"x": 400, "y": 250}}], "wires": [{"from": {"componentId": "bat1", "pointIndex": 0}, "to": {"componentId": "res1", "pointIndex": 0}, "description": "High current path - fast electrons"}, {"from": {"componentId": "res1", "pointIndex": 1}, "to": {"componentId": "res2", "pointIndex": 0}, "description": "Series connection"}, {"from": {"componentId": "res2", "pointIndex": 1}, "to": {"componentId": "led1", "pointIndex": 0}, "description": "To LED anode"}, {"from": {"componentId": "led1", "pointIndex": 1}, "to": {"componentId": "bat1", "pointIndex": 1}, "description": "Return path to battery"}], "expected_results": {"current": "40 mA", "electron_speed": "Visible flowing animation", "led_brightness": "Medium brightness"}}}