import os
import time
import sys

class TextUI:
    """Class for handling text-based user interface"""
    def __init__(self):
        self.text_speed = 1  # 0=Slow, 1=Normal, 2=Fast
        self.battle_animation = True
        self.width = 80
    
    def clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_line(self, char="-"):
        """Print a horizontal line"""
        print(char * self.width)
    
    def print_title(self, title):
        """Print a centered title with decorative lines"""
        self.print_line("=")
        print(title.center(self.width))
        self.print_line("=")
        print()
    
    def print_centered(self, text):
        """Print text centered on screen"""
        print(text.center(self.width))
    
    def print_message(self, message):
        """Print a message with appropriate text speed"""
        print(message)
        self.wait_for_input(0.5 if self.text_speed == 0 else 0.2 if self.text_speed == 1 else 0)
    
    def print_dialog(self, speaker, text):
        """Print dialog with speaker name"""
        print(f"{speaker}: ", end="")
        
        if self.text_speed == 2:  # Fast
            print(text)
            self.wait_for_input(0.5)
        else:
            for char in text:
                print(char, end="", flush=True)
                time.sleep(0.03 if self.text_speed == 1 else 0.06)  # Normal or Slow
            print()
            self.wait_for_input(0.5)
    
    def print_description(self, description):
        """Print a location or item description"""
        lines = []
        words = description.split()
        current_line = ""
        
        for word in words:
            if len(current_line) + len(word) + 1 <= self.width:
                current_line += word + " "
            else:
                lines.append(current_line)
                current_line = word + " "
        
        if current_line:
            lines.append(current_line)
        
        for line in lines:
            print(line)
        
        print()
    
    def menu(self, prompt, options):
        """Display a menu and get user choice"""
        print(prompt)
        print()
        
        for i, option in enumerate(options):
            print(f"{i+1}) {option}")
        
        print()
        
        while True:
            try:
                choice = input("Enter your choice (number): ")
                choice = int(choice) - 1
                
                if 0 <= choice < len(options):
                    return choice
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a number.")
    
    def input_text(self, prompt):
        """Get text input from user"""
        print(prompt)
        return input("> ")
    
    def confirm(self, prompt):
        """Ask for confirmation (yes/no)"""
        print(f"{prompt} (y/n)")
        while True:
            choice = input("> ").lower()
            if choice in ["y", "yes"]:
                return True
            elif choice in ["n", "no"]:
                return False
            else:
                print("Please enter 'y' or 'n'.")
    
    def wait_for_input(self, delay=None):
        """Wait for user input or a delay"""
        if delay:
            time.sleep(delay)
        else:
            input("Press Enter to continue...")
    
    def set_text_speed(self, speed):
        """Set the text display speed"""
        self.text_speed = speed
    
    def set_battle_animation(self, enabled):
        """Enable or disable battle animations"""
        self.battle_animation = enabled
    
    def print_battle_status(self, player_pokemon, enemy_pokemon):
        """Display battle status screen"""
        self.clear_screen()
        
        # Enemy Pokemon
        print(f"Wild {enemy_pokemon.name} (Lv. {enemy_pokemon.level})".ljust(self.width))
        self.print_hp_bar(enemy_pokemon.hp, enemy_pokemon.max_hp, 20)
        print()
        
        # Player Pokemon
        print(f"{player_pokemon.name} (Lv. {player_pokemon.level})".rjust(self.width))
        self.print_hp_bar(player_pokemon.hp, player_pokemon.max_hp, 20, right_align=True)
        print(f"HP: {player_pokemon.hp}/{player_pokemon.max_hp}".rjust(self.width))
        print()
    
    def print_hp_bar(self, current, maximum, length=20, right_align=False):
        """Print a visual HP bar"""
        ratio = current / maximum
        filled = int(length * ratio)
        
        if ratio > 0.5:
            color = "green"
        elif ratio > 0.2:
            color = "yellow"
        else:
            color = "red"
        
        bar = "[" + "=" * filled + " " * (length - filled) + "]"
        
        if right_align:
            print(bar.rjust(self.width))
        else:
            print(bar)
    
    def print_wild_encounter(self, pokemon):
        """Display wild Pokemon encounter message"""
        self.clear_screen()
        self.print_message("Wild Pokemon appeared!")
        time.sleep(0.5)
        self.print_message(f"A wild {pokemon.name} (Lv. {pokemon.level}) appeared!")
        self.wait_for_input()
    
    def show_pokemon_summary(self, pokemon):
        """Show detailed summary of a Pokemon"""
        self.clear_screen()
        self.print_title(f"{pokemon.name} Summary")
        
        print(f"Level: {pokemon.level}")
        print(f"Type: {pokemon.type}")
        print(f"HP: {pokemon.hp}/{pokemon.max_hp}")
        print(f"Attack: {pokemon.attack}")
        print(f"Defense: {pokemon.defense}")
        print(f"Sp. Attack: {pokemon.sp_attack}")
        print(f"Sp. Defense: {pokemon.sp_defense}")
        print(f"Speed: {pokemon.speed}")
        print(f"XP: {pokemon.xp}/{pokemon.xp_to_level}")
        print()
        
        print("Moves:")
        for move in pokemon.moves:
            print(f"- {move['name']} ({move['type']} type, Power: {move['power']})")
        
        self.wait_for_input()
    
    def print_pokemon_stats(self, pokemon):
        """Print basic Pokemon stats"""
        print(f"Type: {pokemon.type}")
        print(f"HP: {pokemon.hp}/{pokemon.max_hp}")
        self.print_hp_bar(pokemon.hp, pokemon.max_hp, 30)
        print(f"Level: {pokemon.level} ({pokemon.xp}/{pokemon.xp_to_level} XP)")
        print()
        
        print("Moves:")
        for move in pokemon.moves:
            print(f"- {move['name']} ({move['type']} type)")
        print()