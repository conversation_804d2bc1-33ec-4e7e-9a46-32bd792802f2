class Player:
    """Class representing the player character"""
    def __init__(self, name="<PERSON>", pokemon=None, position=None, money=1000, badges=None, items=None):
        self.name = name
        self.pokemon = pokemon or []
        self.position = position or {"map": "pallet_town", "x": 5, "y": 5}
        self.money = money
        self.badges = badges or []
        self.items = items or {"Potion": 3, "Pokeball": 5}
        self.pokedex = {}  # Pokemon seen/caught
        self.last_pokemon_center = "pallet_town"  # Last visited Pokemon Center
    
    def add_pokemon(self, pokemon):
        """Add a Pokemon to the player's party"""
        if len(self.pokemon) < 6:
            self.pokemon.append(pokemon)
            # Update Pokedex
            self.pokedex[pokemon.name] = {"seen": True, "caught": True}
            return True
        return False
    
    def switch_pokemon(self, index1, index2):
        """Switch the positions of two Pokemon in the party"""
        if 0 <= index1 < len(self.pokemon) and 0 <= index2 < len(self.pokemon):
            self.pokemon[index1], self.pokemon[index2] = self.pokemon[index2], self.pokemon[index1]
            return True
        return False
    
    def heal_all_pokemon(self):
        """Heal all Pokemon to full HP"""
        for pokemon in self.pokemon:
            pokemon.hp = pokemon.max_hp
    
    def add_item(self, item, quantity=1):
        """Add an item to the player's inventory"""
        if item in self.items:
            self.items[item] += quantity
        else:
            self.items[item] = quantity
    
    def remove_item(self, item, quantity=1):
        """Remove an item from the player's inventory"""
        if item in self.items and self.items[item] >= quantity:
            self.items[item] -= quantity
            if self.items[item] == 0:
                del self.items[item]
            return True
        return False
    
    def add_badge(self, badge):
        """Add a gym badge to the player's collection"""
        if badge not in self.badges:
            self.badges.append(badge)
    
    def has_badge(self, badge):
        """Check if the player has a specific badge"""
        return badge in self.badges
    
    def mark_pokemon_seen(self, pokemon_name):
        """Mark a Pokemon as seen in the Pokedex"""
        if pokemon_name not in self.pokedex:
            self.pokedex[pokemon_name] = {"seen": True, "caught": False}
        else:
            self.pokedex[pokemon_name]["seen"] = True
    
    def to_dict(self):
        """Convert player data to dictionary for saving"""
        return {
            "name": self.name,
            "pokemon": [p.to_dict() for p in self.pokemon],
            "position": self.position,
            "money": self.money,
            "badges": self.badges,
            "items": self.items,
            "pokedex": self.pokedex,
            "last_pokemon_center": self.last_pokemon_center
        }
    
    @classmethod
    def from_dict(cls, data, pokemon_factory):
        """Create a player from saved data"""
        player = cls(
            name=data["name"],
            position=data["position"],
            money=data["money"],
            badges=data["badges"],
            items=data["items"]
        )
        
        # Recreate Pokemon
        for pokemon_data in data["pokemon"]:
            player.pokemon.append(pokemon_factory.create_from_dict(pokemon_data))
        
        # Load Pokedex
        player.pokedex = data["pokedex"]
        player.last_pokemon_center = data.get("last_pokemon_center", "pallet_town")
        
        return player